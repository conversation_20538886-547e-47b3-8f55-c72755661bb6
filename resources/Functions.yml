PGMigrate:
  name: ${self:provider.stackName}-PGMigrate
  image:
    name: isolated-proxy
    command:
      - sentry_sdk.integrations.init_serverless_sdk.sentry_lambda_handler
    entryPoint:
      - /opt/compose/entrypoint.sh
  environment:
    SENTRY_INITIAL_HANDLER: isolated_proxy.functions.common.pg_migrate
  role: IAMRole
  timeout: 600

GetProxy:
  name: ${self:provider.stackName}-GetProxy
  role: IAMRole
  timeout: 120
  memorySize: 512
  environment:
    SENTRY_INITIAL_HANDLER: isolated_proxy.functions.proxy.get_proxy
  image:
    name: isolated-proxy
    command:
      - sentry_sdk.integrations.init_serverless_sdk.sentry_lambda_handler
    entryPoint:
      - /opt/compose/entrypoint.sh

BlockProxy:
  name: ${self:provider.stackName}-BlockProxy
  role: IAMRole
  timeout: 120
  memorySize: 512
  environment:
    SENTRY_INITIAL_HANDLER: isolated_proxy.functions.proxy.block_proxy
  image:
    name: isolated-proxy
    command:
      - sentry_sdk.integrations.init_serverless_sdk.sentry_lambda_handler
    entryPoint:
      - /opt/compose/entrypoint.sh

UpdateProxyPoolStatus:
  name: ${self:provider.stackName}-UpdateProxyPoolStatus
  role: IAMRole
  timeout: 600
  memorySize: 512
  environment:
    SENTRY_INITIAL_HANDLER: isolated_proxy.functions.proxy.update_pool_status
  image:
    name: isolated-proxy
    command:
      - sentry_sdk.integrations.init_serverless_sdk.sentry_lambda_handler
    entryPoint:
      - /opt/compose/entrypoint.sh

CheckBalance:
  name: ${self:provider.stackName}-CheckBalance
  role: IAMRole
  timeout: 120
  memorySize: 512
  environment:
    SENTRY_INITIAL_HANDLER: isolated_proxy.functions.common.check_balance
  image:
    name: isolated-proxy
    command:
      - sentry_sdk.integrations.init_serverless_sdk.sentry_lambda_handler
    entryPoint:
      - /opt/compose/entrypoint.sh
  events:
    - schedule: rate(3 hours)

SetProxies:
  name: ${self:provider.stackName}-SetProxies
  role: IAMRole
  timeout: 360
  memorySize: 256
  events:
    - schedule:
        rate: rate(1 hour)
  environment:
    SENTRY_INITIAL_HANDLER: isolated_proxy.functions.proxy.set_proxies
  image:
    name: isolated-proxy
    command:
      - sentry_sdk.integrations.init_serverless_sdk.sentry_lambda_handler
    entryPoint:
      - /opt/compose/entrypoint.sh
  maximumRetryAttempts: 0

CheckBalanceSmartProxy:
  name: ${self:provider.stackName}-CheckBalanceSmartProxy
  role: IAMRole
  timeout: 600
  memorySize: 10240
  architecture: arm64
  environment:
    SENTRY_INITIAL_HANDLER: isolated_proxy.functions.common.smart_proxy_checker
  image:
    name: isolated-proxy-smartproxy
    command:
      - sentry_sdk.integrations.init_serverless_sdk.sentry_lambda_handler
    entryPoint:
      - /opt/compose/entrypoint.sh
  events:
    - schedule:
        rate: cron(0 * * * ? *)
        enabled: ${file(resources/${self:custom.stage}_env.yml):ENABLE_BROWSER_CHECK_SMARTPROXY}
  maximumRetryAttempts: 0

CheckRequestSmartProxy:
  name: ${self:provider.stackName}-CheckRequestSmartProxy
  role: IAMRole
  timeout: 100
  memorySize: 512
  architecture: arm64
  environment:
    SENTRY_INITIAL_HANDLER: isolated_proxy.functions.common.smart_proxy_request_checker
  image:
    name: isolated-proxy-smartproxy
    command:
      - sentry_sdk.integrations.init_serverless_sdk.sentry_lambda_handler
    entryPoint:
      - /opt/compose/entrypoint.sh
  events:
    - schedule:
        rate: cron(0/15 * * * ? *)
        enabled: ${file(resources/${self:custom.stage}_env.yml):ENABLE_REQUESTS_CHECK_SMARTPROXY}
  maximumRetryAttempts: 0

BlocklistedIpsCleaner:
  name: ${self:provider.stackName}-BlocklistedIpsCleaner
  role: IAMRole
  timeout: 600
  memorySize: 256
  environment:
    SENTRY_INITIAL_HANDLER: isolated_proxy.functions.common.blocklisted_cleaner
  image:
    name: isolated-proxy
    command:
      - sentry_sdk.integrations.init_serverless_sdk.sentry_lambda_handler
    entryPoint:
      - /opt/compose/entrypoint.sh
  events:
    - schedule:
        rate: cron(0 13 * * ? *)
  maximumRetryAttempts: 0
