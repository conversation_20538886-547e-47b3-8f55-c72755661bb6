Resources:
  IAMRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Statement:
        - Action: sts:AssumeRole
          Effect: Allow
          Principal:
            Service: lambda.amazonaws.com
        Version: '2012-10-17'
      Policies:
      - PolicyDocument:
          Statement:
          - Action:
              - "secretsmanager:*"
            Effect: Allow
            Resource: "*"
          - Action: "*"
            Effect: Allow
            Resource:
              - Fn::Join:
                  - ':'
                  - - 'arn:aws:rds'
                    - Ref: 'AWS::Region'
                    - Ref: 'AWS::AccountId'
                    - 'cluster'
                    - !Ref IsolatedProxyDBInstance
          - Action:
            - "s3:*"
            Effect: "Allow"
            Resource:
              - "arn:aws:s3:::*/*"
              - "arn:aws:s3:::*"
          - Action:
            - "cloudwatch:PutMetricData"
            Effect: "Allow"
            Resource: "*"
          - Action:
            - logs:CreateLogGroup
            - logs:CreateLogStream
            - logs:PutLogEvents
            - ec2:CreateNetworkInterface
            - ec2:DescribeNetworkInterfaces
            - ec2:DeleteNetworkInterface
            - ec2:AssignPrivateIpAddresses
            - ec2:UnassignPrivateIpAddresses
            - secretsmanager:GetRandomPassword
            - secretsmanager:GetResourcePolicy
            - secretsmanager:GetSecretValue
            - secretsmanager:DescribeSecret
            - secretsmanager:ListSecretVersionIds
            Effect: "Allow"
            Resource: "*"
          - Action:
            - "dynamodb:*"
            Effect: "Allow"
            Resource: "*"
          - Action:
              - "sqs:*"
            Effect: "Allow"
            Resource: "*"
          Version: '2012-10-17'
        PolicyName: ${self:provider.stackName}-Policy
      RoleName: ${self:provider.stackName}-Role
