Resources:
  IsolatedProxyDBSecurityGroup:
    Type: 'AWS::EC2::SecurityGroup'
    Properties:
      GroupDescription: !Ref 'AWS::StackName'
      VpcId: ${file(resources/${self:custom.stage}_env.yml):VPC_ID}
      Tags:
      - Key: Name
        Value: ${self:provider.stackName}-AuroraClusterSecurityGroup

  SGIngress:
    Type: AWS::EC2::SecurityGroupIngress
    DependsOn: IsolatedProxyDBSecurityGroup
    Properties:
      Description: "RDS port ingress Self Reference"
      FromPort: '5432'
      GroupId: !Ref IsolatedProxyDBSecurityGroup
      IpProtocol: tcp
      SourceSecurityGroupId: !Ref IsolatedProxyDBSecurityGroup
      ToPort: '5432'

  SGIngressBastion:
    Type: 'AWS::EC2::SecurityGroupIngress'
    Properties:
      Description: "Bastion"
      FromPort: '5432'
      GroupId: !Ref IsolatedProxyDBSecurityGroup
      IpProtocol: tcp
      SourceSecurityGroupId: ${file(resources/${self:custom.stage}_env.yml):BASTION_SECURITY_GROUP}
      ToPort: '5432'

  IsolatedProxyDBSubnetGroup:
    Type: AWS::RDS::DBSubnetGroup
    Properties:
      DBSubnetGroupDescription: "Subnet group for isolated-proxy RDS instance"
      SubnetIds:
        - ${file(resources/${self:custom.stage}_env.yml):SUBNET_ID1}
        - ${file(resources/${self:custom.stage}_env.yml):SUBNET_ID2}
      DBSubnetGroupName: !Sub "${AWS::StackName}-db-subnet-group"

  ClusterSecretProvisioned:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: ${self:provider.stackName}-DBClusterSecretProvisioned
      Description: 'This is the Master secret for the RDS PostgreSQL instance'
      GenerateSecretString:
        SecretStringTemplate: '{"username": "isolated_proxy"}'
        GenerateStringKey: 'password'
        PasswordLength: 16
        ExcludeCharacters: '! " # $ % & ( ) * + , - . / : ; < = > ? @ [ \ ] ^ _ ` { | } ~'

  IsolatedProxyDBInstance:
    Type: AWS::RDS::DBInstance
    Properties:
      DBInstanceIdentifier: ${self:provider.stackName}-db-instance
      DBName: "isolated_proxy"
      AllocatedStorage: 20
      DBInstanceClass: db.t3.micro
      Engine: postgres
      EngineVersion: "13.12"
      MasterUsername: !Join [ '', [ '{{resolve:secretsmanager:', !Ref ClusterSecretProvisioned, ':SecretString:username}}' ] ]
      MasterUserPassword: !Join [ '', [ '{{resolve:secretsmanager:', !Ref ClusterSecretProvisioned, ':SecretString:password}}' ] ]
      PubliclyAccessible: false
      StorageType: gp2
      BackupRetentionPeriod: 0
      MultiAZ: false
      VPCSecurityGroups:
        - !Ref IsolatedProxyDBSecurityGroup
      DBSubnetGroupName: !Ref IsolatedProxyDBSubnetGroup
      DBParameterGroupName: !Ref IsolatedProxyDBParameterGroup
      EnableCloudwatchLogsExports:
        - iam-db-auth-error
        - postgresql
        - upgrade

  IsolatedProxyDBParameterGroup:
    Type: AWS::RDS::DBParameterGroup
    Properties:
      Description: "Parameter group for enabling RDS PostgreSQL logging"
      Family: postgres13
      Parameters:
        log_statement: "all"
        log_min_duration_statement: "0"
        log_connections: "1"
        log_disconnections: "1"
        log_duration: "1"
        log_hostname: "1"
        log_timezone: "UTC"

  ProxyIAMRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: rds.amazonaws.com
            Action: "sts:AssumeRole"
      Policies:
        - PolicyName: RDSProxyAccessPolicy
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - "secretsmanager:GetSecretValue"
                Resource: !Ref ClusterSecretProvisioned

  DBProxyProvisioned:
    Type: AWS::RDS::DBProxy
    Properties:
      Auth:
        - AuthScheme: SECRETS
          IAMAuth: DISABLED  # Disable IAM authentication if you're using Secrets Manager
          SecretArn: !Ref ClusterSecretProvisioned  # Use the secret for the DB credentials
      DBProxyName: !Sub ${AWS::StackName}-proxy
      DebugLogging: false
      EngineFamily: POSTGRESQL
      IdleClientTimeout: 1800
      RequireTLS: true
      RoleArn: !GetAtt ProxyIAMRole.Arn  # Attach the IAM role created for the proxy
      VpcSecurityGroupIds:
        - !Ref IsolatedProxyDBSecurityGroup  # Use the existing security group for the RDS instance
      VpcSubnetIds:
        - ${file(resources/${self:custom.stage}_env.yml):SUBNET_ID1}
        - ${file(resources/${self:custom.stage}_env.yml):SUBNET_ID2}

  ProxyTargetProvisioned:
    Type: AWS::RDS::DBProxyTargetGroup
    Properties:
      DBProxyName: !Ref DBProxyProvisioned
      TargetGroupName: default
      DBInstanceIdentifiers:
        - !Ref IsolatedProxyDBInstance  # Reference your existing RDS instance

Outputs:
  DBInstanceEndpoint:
    Description: "RDS PostgreSQL Endpoint"
    Value: !GetAtt IsolatedProxyDBInstance.Endpoint.Address

  DBInstancePort:
    Description: "RDS PostgreSQL Port"
    Value: !GetAtt IsolatedProxyDBInstance.Endpoint.Port

  DBSecretArn:
    Description: "ARN of the Secrets Manager secret containing the master credentials"
    Value: !Ref ClusterSecretProvisioned
