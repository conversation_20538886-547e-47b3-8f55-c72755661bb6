SHELL := /bin/bash

LOCAL_NODE_MODULES = ./node_modules/.bin/serverless

ifeq ($(shell test -e $(LOCAL_NODE_MODULES) && echo -n yes),yes)
    SERVERLESS=$(LOCAL_NODE_MODULES)
else
    SERVERLESS=serverless
endif

define check_env
	@[ "${AWS_REGION}" ] && echo "aws_region $(AWS_REGION)" || ( echo "AWS_REGION is not set"; exit 1 )
endef

bdist:
	pip install wheel
	python setup.py bdist_wheel

	cp dist/isolated_proxy-0.0.1-py2.py3-none-any.whl compose/isolated_proxy/isolated_proxy-0.0.1-py2.py3-none-any.whl
	cp dist/isolated_proxy-0.0.1-py2.py3-none-any.whl compose/isolated_proxy_smartproxy/isolated_proxy-0.0.1-py2.py3-none-any.whl

	cp requirements/prod.txt compose/isolated_proxy/requirements/prod.txt
	cp requirements/private.txt compose/isolated_proxy/requirements/private.txt

	cp requirements/prod.txt compose/isolated_proxy_smartproxy/requirements/prod.txt
	cp requirements/private.txt compose/isolated_proxy_smartproxy/requirements/private.txt
	cp requirements/selenium.txt compose/isolated_proxy_smartproxy/requirements/selenium.txt

sls_deploy:
	$(call check_env)
	SLS_DEBUG=* $(SERVERLESS) deploy --verbose

check:
	brunette src --check
	brunette setup.py --check

clean:
	isort src

	brunette src

	isort setup.py
	brunette setup.py

install_node:
	npm install --save-dev

install_python:
	pip install -e .
	pip install -r requirements/dev.txt

lint:
	flake8 --show-source src
	isort --check-only src --diff

	flake8 --show-source setup.py
	isort --check-only setup.py --diff

download_geoip_local:
	$(call check_env)
	$(call check_license_key)

	 ./scripts/download_geoip.sh

download_geoip: download_geoip_local
	rm -rf GeoLi*

copy_requirements:
	cp requirements/prod.txt compose/isolated_proxy/requirements/prod.txt

	cp requirements/prod.txt compose/isolated_proxy_smartproxy/requirements/prod.txt
	cp requirements/private.txt compose/isolated_proxy_smartproxy/requirements/private.txt
	cp requirements/selenium.txt compose/isolated_proxy_smartproxy/requirements/selenium.txt

pg_migrate:
	alembic upgrade head

sls_migrate_pg:
	$(call check_env)
	$(SERVERLESS) invoke -f PGMigrate

download_proxy_extension:
	$(eval CAPSOLVER_VERSION := $(shell jq -r '.version' requirements/captcha_version.json))
	aws s3 cp --recursive "s3://plutus-dispute-${BRANCH}-bucket/capsolver_$(CAPSOLVER_VERSION)" compose/isolated_proxy_smartproxy/proxy_extension/capsolver

download_proxy_extension_locally:
	$(eval CAPSOLVER_VERSION := $(shell jq -r '.version' requirements/captcha_version.json))
	aws s3 cp --recursive "s3://plutus-dispute-${BRANCH}-bucket/capsolver_$(CAPSOLVER_VERSION)" proxy_extension/capsolver
