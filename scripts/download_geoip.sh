#!/bin/bash
set -e

LICENSE_KEY=$LICENSE_KEY
BRANCH=$BRANCH
S3_BUCKET_NAME="plutus-dispute-$BRANCH-bucket"
S3_PATH="geoip/"
TARGET_DIR1="./compose/isolated_proxy"
TARGET_DIR2="./"
TARGET_DIR3="./compose/isolated_proxy_smartproxy"

check_download_upload() {
    local db_type=$1
    local file_name="GeoLite2-${db_type}.mmdb"

    echo "Checking if ${file_name} is available and recent in S3..."
    if ! aws s3api head-object --bucket "${S3_BUCKET_NAME}" --key "${S3_PATH}${file_name}" &>/dev/null || \
       ! aws s3 cp "s3://${S3_BUCKET_NAME}/${S3_PATH}${file_name}" "${TARGET_DIR1}/${file_name}" --only-show-errors || \
       ! aws s3 cp "s3://${S3_BUCKET_NAME}/${S3_PATH}${file_name}" "${TARGET_DIR2}/${file_name}" --only-show-errors || \
       ! aws s3 cp "s3://${S3_BUCKET_NAME}/${S3_PATH}${file_name}" "${TARGET_DIR3}/${file_name}" --only-show-errors || \
       ! find "${TARGET_DIR1}" -name "${file_name}" -mtime -1 | grep -q . || \
       ! find "${TARGET_DIR2}" -name "${file_name}" -mtime -1 | grep -q . || \
       ! find "${TARGET_DIR3}" -name "${file_name}" -mtime -1 | grep -q . ; then

        echo "Downloading ${file_name} due to absence or outdated version in S3..."

        wget "https://download.maxmind.com/app/geoip_download?edition_id=GeoLite2-${db_type}&license_key=${LICENSE_KEY}&suffix=tar.gz"
        tar -xf "geoip_download?edition_id=GeoLite2-${db_type}&license_key=${LICENSE_KEY}&suffix=tar.gz"

        cp "./${file_name}" "${TARGET_DIR1}/"
        cp "./${file_name}" "${TARGET_DIR3}/"

        aws s3 cp "${file_name}" "s3://${S3_BUCKET_NAME}/${S3_PATH}" --only-show-errors

        rm -rf geoip_downlo*

    else
        echo "${file_name} is recent and has been downloaded from S3."
    fi
}

check_download_upload "Country"
check_download_upload "City"
