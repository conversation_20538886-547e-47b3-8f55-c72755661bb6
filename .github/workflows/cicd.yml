name: CI/CD

on: push

jobs:
  lint:
    runs-on: ubuntu-24.04

    steps:
      - name: Git Checkout
        uses: actions/checkout@v4

      - name: Install Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"
          cache: "pip"
          cache-dependency-path: "requirements/dev.txt"

      - name: Install Requirements
        run: |
          make install_python

      - name: Check Project
        run: |
          make check

      - name: Lint Project
        run: |
          make lint

      - name: Branch output
        id: branch
        run: echo "branch=${GITHUB_REF#refs/*/}" >> $GITHUB_OUTPUT

      - name: Commit sha output
        id: commit_sha
        run: |
          echo "commit_sha=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

      - name: AWS Access Key output
        id: aws_access_key
        run: |
          branch="${GITHUB_REF##*/}"

          if [ "$branch" = "master" ]; then
            secret_name="AWS_PROD_ACCESS_KEY_ID"
            echo "production"
          else
            secret_name="AWS_DEV_ACCESS_KEY_ID"
            echo "develop"
          fi

          echo "aws_access_key_secret_name=$secret_name" >> $GITHUB_OUTPUT

      - name: AWS Secret Key output
        id: aws_secret_key
        run: |
          branch="${GITHUB_REF##*/}"

          if [ "$branch" = "master" ]; then
            secret_name="AWS_PROD_SECRET_ACCESS_KEY"
            echo "production"
          else
            secret_name="AWS_DEV_SECRET_ACCESS_KEY"
            echo "develop"
          fi

          echo "aws_secret_key_secret_name=$secret_name" >> $GITHUB_OUTPUT

    outputs:
      branch: ${{ steps.branch.outputs.branch }}
      commit_sha: ${{ steps.commit_sha.outputs.commit_sha }}
      aws_access_key_secret_name: ${{ steps.aws_access_key.outputs.aws_access_key_secret_name }}
      aws_secret_key_secret_name: ${{ steps.aws_secret_key.outputs.aws_secret_key_secret_name }}

  build_image:
    needs: [ lint ]
    runs-on: ubuntu-24.04

    if: github.ref == 'refs/heads/master' || github.ref == 'refs/heads/develop'

    strategy:
      matrix:
        image: [ "isolated_proxy" ]

    steps:
      - name: Git Checkout
        uses: actions/checkout@v4

      - name: Prepare Env Variables
        run: |
          echo "AWS_REGION=${{ vars.AWS_DEFAULT_REGION }}" >> $GITHUB_ENV
          echo "AWS_DEFAULT_REGION=${{ vars.AWS_DEFAULT_REGION }}" >> $GITHUB_ENV
          echo "LICENSE_KEY=${{ secrets.LICENSE_KEY }}" >> $GITHUB_ENV
          echo "BRANCH=${{ needs.lint.outputs.branch }}" >> $GITHUB_ENV

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-region: ${{ vars.AWS_DEFAULT_REGION }}
          aws-access-key-id: ${{ secrets[needs.lint.outputs.aws_access_key_secret_name] }}
          aws-secret-access-key: ${{ secrets[needs.lint.outputs.aws_secret_key_secret_name] }}

      - name: Copy Requirements
        run: |
          make copy_requirements
          make bdist
          make download_geoip
          make download_proxy_extension

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
        with:
          mask-password: "true"

      - name: Create ECR repository if it doesn't exist
        id: create-ecr-repository
        run: |
          REPO_NAME=${{ matrix.image }}
          POLICY=$(cat scripts/ecr_cleanup_policy.json)
          aws ecr describe-repositories --repository-names "$REPO_NAME" ||
          aws ecr create-repository --repository-name "$REPO_NAME" --image-scanning-configuration scanOnPush=true --image-tag-mutability MUTABLE &&
          aws ecr put-lifecycle-policy --repository-name "$REPO_NAME" --lifecycle-policy-text "$POLICY"
        env:
          AWS_REGION: ${{ vars.AWS_DEFAULT_REGION }}
          AWS_DEFAULT_REGION: ${{ vars.AWS_DEFAULT_REGION }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPOSITORY }}
          tags: |
            type=sha
            type=raw,value=latest
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ${{ matrix.image }}

      - name: Docker build
        uses: docker/build-push-action@v5
        with:
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          context: compose/${{ matrix.image }}
          provenance: false
          build-args: |
            AWS_ACCESS_KEY_ID=${{ secrets.AWS_PROD_ACCESS_KEY_ID }}
            AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_PROD_SECRET_ACCESS_KEY }}
            DOMAIN=${{ vars.DOMAIN }}
            REPOSITORY=${{ vars.REPOSITORY }}
            DOMAIN_OWNER=${{ vars.MASTER_DOMAIN_OWNER }}
            AWS_REGION=${{ vars.AWS_DEFAULT_REGION }}
          cache-from: type=registry,ref=${{ steps.login-ecr.outputs.registry }}/${{ matrix.image }}:latest
          cache-to: mode=max,image-manifest=true,type=registry,ref=${{ steps.login-ecr.outputs.registry }}/${{ matrix.image }}:latest

  build_image_arm:
    needs: [ lint ]
    runs-on: ubuntu-24.04

    if: github.ref == 'refs/heads/master' || github.ref == 'refs/heads/develop'

    strategy:
      matrix:
        image: [ "isolated_proxy_smartproxy" ]

    steps:
      - name: Git Checkout
        uses: actions/checkout@v4

      - name: Prepare Env Variables
        run: |
          echo "AWS_REGION=${{ vars.AWS_DEFAULT_REGION }}" >> $GITHUB_ENV
          echo "AWS_DEFAULT_REGION=${{ vars.AWS_DEFAULT_REGION }}" >> $GITHUB_ENV
          echo "LICENSE_KEY=${{ secrets.LICENSE_KEY }}" >> $GITHUB_ENV
          echo "BRANCH=${{ needs.lint.outputs.branch }}" >> $GITHUB_ENV

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-region: ${{ vars.AWS_DEFAULT_REGION }}
          aws-access-key-id: ${{ secrets[needs.lint.outputs.aws_access_key_secret_name] }}
          aws-secret-access-key: ${{ secrets[needs.lint.outputs.aws_secret_key_secret_name] }}

      - name: Copy Requirements
        run: |
          make copy_requirements
          make bdist
          make download_geoip 
          make download_proxy_extension

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
        with:
          mask-password: "true"

      - name: Create ECR repository if it doesn't exist
        id: create-ecr-repository
        run: |
          REPO_NAME=${{ matrix.image }}
          POLICY=$(cat scripts/ecr_cleanup_policy.json)
          aws ecr describe-repositories --repository-names "$REPO_NAME" ||
          aws ecr create-repository --repository-name "$REPO_NAME" --image-scanning-configuration scanOnPush=true --image-tag-mutability MUTABLE &&
          aws ecr put-lifecycle-policy --repository-name "$REPO_NAME" --lifecycle-policy-text "$POLICY"
        env:
          AWS_REGION: ${{ vars.AWS_DEFAULT_REGION }}
          AWS_DEFAULT_REGION: ${{ vars.AWS_DEFAULT_REGION }}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPOSITORY }}
          tags: |
            type=sha
            type=raw,value=latest
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ${{ matrix.image }}

      - name: Docker build
        uses: docker/build-push-action@v5
        with:
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          context: compose/${{ matrix.image }}
          provenance: false
          platforms: linux/arm64
          build-args: |
            AWS_ACCESS_KEY_ID=${{ secrets.AWS_PROD_ACCESS_KEY_ID }}
            AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_PROD_SECRET_ACCESS_KEY }}
            DOMAIN=${{ vars.DOMAIN }}
            REPOSITORY=${{ vars.REPOSITORY }}
            DOMAIN_OWNER=${{ vars.MASTER_DOMAIN_OWNER }}
            AWS_REGION=${{ vars.AWS_DEFAULT_REGION }}
          cache-from: type=registry,ref=${{ steps.login-ecr.outputs.registry }}/${{ matrix.image }}:latest
          cache-to: mode=max,image-manifest=true,type=registry,ref=${{ steps.login-ecr.outputs.registry }}/${{ matrix.image }}:latest

  deploy_isolated_proxy:
    runs-on: ubuntu-24.04
    needs: [lint, build_image, build_image_arm]

    if: github.ref == 'refs/heads/master' || github.ref == 'refs/heads/develop'

    steps:
      - name: Git Checkout
        uses: actions/checkout@v3

      - name: Prepare Credentials
        run: |
          echo "AWS_ACCESS_KEY_ID=${{ secrets[needs.lint.outputs.aws_access_key_secret_name] }}" >> $GITHUB_ENV
          echo "AWS_SECRET_ACCESS_KEY=${{ secrets[needs.lint.outputs.aws_secret_key_secret_name] }}" >> $GITHUB_ENV
          echo "AWS_REGION=${{ vars.AWS_DEFAULT_REGION }}" >> $GITHUB_ENV
          echo "AWS_DEFAULT_REGION=${{ vars.AWS_DEFAULT_REGION }}" >> $GITHUB_ENV
          echo "COMMIT_SHA=${{ needs.lint.outputs.commit_sha }}" >> $GITHUB_ENV
          echo "BRANCH=${{ needs.lint.outputs.branch }}" >> $GITHUB_ENV
          echo "PROXY_ECR_URL=${{ vars[format('{0}_DOMAIN_OWNER', needs.lint.outputs.branch)] }}.dkr.ecr.${{ vars.AWS_REGION }}.amazonaws.com/isolated_proxy:sha-${{ needs.lint.outputs.commit_sha }}" >> $GITHUB_ENV
          echo "SMARTPROXY_ECR_URL=${{ vars[format('{0}_DOMAIN_OWNER', needs.lint.outputs.branch)] }}.dkr.ecr.${{ vars.AWS_REGION }}.amazonaws.com/isolated_proxy_smartproxy:sha-${{ needs.lint.outputs.commit_sha }}" >> $GITHUB_ENV

      - name: Install NodeJS
        uses: actions/setup-node@v1
        with:
          node-version: "20.x"

      - name: Serverless Install
        run: |
          make install_node

      - name: Serverless Deploy
        run: |
          make sls_deploy

      - name: Serverless Migrate PG
        run: |
          make sls_migrate_pg
