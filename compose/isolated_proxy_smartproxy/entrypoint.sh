#!/bin/bash
set -e

watchgod_xvfb() {
  while :; do
    if ! pgrep -x "Xvfb" >/dev/null 2>&1; then
         start_xvfb
    fi
    sleep 1
  done
}

check_xvfb() {
  echo "Check for xvfb..."

  if xdpyinfo -display "${DISPLAY}" >/dev/null 2>&1; then
    return 0
  else
    return 1
  fi
}

wait_for_xvfb() {
  MAX_ATTEMPTS=30
  COUNT=0
  echo "Waiting for Xvfb to be ready..."
  while ! check_xvfb; do
    sleep 1s
    COUNT=$(( COUNT + 1 ))
    if [ "${COUNT}" -ge "${MAX_ATTEMPTS}" ]; then
      echo "Gave up waiting for X server on ${DISPLAY}"
      exit 1
    fi
  done
  echo "Xvfb is ready!!!"
}

start_xvfb() {
  echo "Starting Xvfb..."
  [ -f /tmp/.X99-lock ] && rm -f /tmp/.X99-lock
  Xvfb "${DISPLAY}" -screen 0 "${SCREEN}" -nolisten tcp -nolisten unix &
  wait_for_xvfb
}

watchgod_xvfb &

source /app/isolated_proxy_venv/bin/activate

if [ -z "${AWS_LAMBDA_RUNTIME_API}" ]; then
  # pip3 install -e .
  exec "$@"
else
  exec python3 -m awslambdaric $1
fi
