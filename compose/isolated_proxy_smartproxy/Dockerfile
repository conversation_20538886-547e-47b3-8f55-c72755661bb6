FROM debian:bookworm

ARG AWS_ACCESS_KEY_ID
ARG AWS_SECRET_ACCESS_KEY
ARG DOMAIN
ARG REPOSITORY
ARG DOMAIN_OWNER
ARG AWS_REGION

RUN echo "cache"

RUN mkdir /opt/scripts

COPY ./scripts/dependencies.sh /opt/scripts/dependencies.sh
RUN chmod +x /opt/scripts/dependencies.sh
RUN /opt/scripts/dependencies.sh

COPY ./scripts/install_xvfb.sh /opt/scripts/install_xvfb.sh
RUN chmod +x /opt/scripts/install_xvfb.sh
RUN /opt/scripts/install_xvfb.sh

ENV SCREEN=1440x900x24
ENV DISPLAY=:99

RUN apt-get -y update
RUN apt-get -y upgrade

RUN apt-get -y install python3-pip python3-dev python3-setuptools libpq-dev libnss3 libxml2-dev libxslt-dev

RUN apt-get install gcc -y
RUN apt-get install wget -y
RUN apt-get install unzip -y
RUN apt-get install curl -y --fix-missing
RUN apt-get install cargo -y

RUN apt-get -y -qq --no-install-recommends install \
    fontconfig \
    fonts-freefont-ttf \
    fonts-gfs-neohellenic \
    fonts-indic \
    fonts-ipafont-gothic \
    fonts-kacst \
    fonts-liberation \
    fonts-noto-cjk \
    fonts-noto-color-emoji \
    fonts-roboto \
    fonts-thai-tlwg \
    fonts-wqy-zenhei \
    xfonts-utils

RUN apt-get install net-tools -y
RUN apt-get install x11vnc -y
RUN apt-get install gnome-screenshot -y
RUN apt-get install python3-tk python3-dev -y

RUN echo "no cache"
RUN apt install chromium -y --fix-missing

RUN wget -qP /tmp/ "https://github.com/electron/electron/releases/download/v34.0.2/chromedriver-v34.0.2-linux-arm64.zip" \
  && unzip -o /tmp/chromedriver-v34.0.2-linux-arm64.zip -d /tmp \
  && mv /tmp/chromedriver /usr/bin \
  && chown root:root /usr/bin/chromedriver \
  && chmod +x /usr/bin/chromedriver

RUN apt-get clean
RUN rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/* /root/.cache/* /usr/share/fonts/truetype/noto

COPY ./extensions/tmp_cleaner_extension/tmp_cleaner_extension /opt/tmp_cleaner_extension
RUN chmod +x /opt/tmp_cleaner_extension/extension.py

COPY ./extensions/tmp_cleaner_extension/extensions/ /opt/extensions
RUN chmod +x /opt/extensions/tmp_cleaner_extension

COPY ./requirements /opt/compose/requirements

RUN apt-get -y update
RUN apt install python3.11-venv -y
RUN python3 -m venv /app/isolated_proxy_venv

RUN /app/isolated_proxy_venv/bin/pip install -r /opt/compose/requirements/prod.txt
RUN /app/isolated_proxy_venv/bin/pip install -r /opt/compose/requirements/selenium.txt

RUN /app/isolated_proxy_venv/bin/python -c "from undetected_chromedriver.patcher import Patcher; patcher = Patcher(executable_path=\"/usr/bin/chromedriver\"); assert patcher.patch()"

RUN /app/isolated_proxy_venv/bin/pip install awscli
RUN /app/isolated_proxy_venv/bin/pip install -r /opt/compose/requirements/private.txt --extra-index-url="https://aws:$(/app/isolated_proxy_venv/bin/aws codeartifact get-authorization-token --domain ${DOMAIN} --domain-owner ${DOMAIN_OWNER} --region ${AWS_REGION} --query authorizationToken --output text)@${DOMAIN}-${DOMAIN_OWNER}.d.codeartifact.${AWS_REGION}.amazonaws.com/pypi/${REPOSITORY}/simple/"

RUN mkdir /opt/code

WORKDIR /opt/code

COPY ./entrypoint.sh /opt/compose/entrypoint.sh

COPY ./isolated_proxy-0.0.1-py2.py3-none-any.whl /opt/compose/isolated_proxy-0.0.1-py2.py3-none-any.whl

RUN /app/isolated_proxy_venv/bin/pip install /opt/compose/isolated_proxy-0.0.1-py2.py3-none-any.whl

RUN rm -rf /root/.cache/*

COPY --from=public.ecr.aws/sentry/sentry-python-serverless-sdk:6 /opt/python /app/isolated_proxy_venv/lib/python3.11/dist-packages

RUN echo "no cache"

COPY ./proxy_extension/capsolver /opt/extensions/capsolver

ENV VIRTUAL_ENV=/app/isolated_proxy_venv
ENV PATH="$VIRTUAL_ENV/bin:$PATH"

COPY ./GeoLite2-Country.mmdb /opt/
COPY ./GeoLite2-City.mmdb /opt/

ENV ISOLATED_PROXY_SELENIUM_LAMBDA=1

ENTRYPOINT ["/opt/compose/entrypoint.sh"]
