#!/usr/bin/env python3

import json
import os
import requests
# import subprocess
import time

from path import Path


LAMBDA_EXTENSION_NAME = Path(__file__).parent.name
EVENTS = ['INVOKE', 'SHUTDOWN']


def register_extension():
    headers = {'Lambda-Extension-Name': LAMBDA_EXTENSION_NAME}
    payload = {'events': EVENTS}

    response = requests.post(
        url=f"http://{os.environ['AWS_LAMBDA_RUNTIME_API']}/2020-01-01/extension/register",  # noqa
        json=payload,
        headers=headers
    )

    ext_id = response.headers['Lambda-Extension-Identifier']

    print(f"[{LAMBDA_EXTENSION_NAME}] Registered with ID: {ext_id}", flush=True)

    return ext_id


def process_events(ext_id):
    headers = {
        'Lambda-Extension-Identifier': ext_id
    }

    tmp = Path("/tmp")

    print("EXTENSION PRE WHILE", flush=True)

    try:
        while True:
            response = requests.get(
                url=f"http://{os.environ['AWS_LAMBDA_RUNTIME_API']}/2020-01-01/extension/event/next",  # noqa
                headers=headers,
                timeout=None,
            )

            event = json.loads(response.text)

            # try:
            #     print("EXTENSION DU LOGS", subprocess.check_output("du /tmp/* -sh", shell=True).decode(), flush=True)
            # except Exception as exc:
            #     print("EXTENSION DU LOGS EXC", exc, flush=True)

            # try:
            #     print("EXTENSION PS LOGS", subprocess.check_output("ps aux", shell=True).decode(), flush=True)
            # except Exception as exc:
            #     print("EXTENSION PS LOGS EXC", exc, flush=True)

            # try:
            #     print("EXTENSION FREE LOGS", subprocess.check_output("free -m", shell=True).decode(), flush=True)
            # except Exception as exc:
            #     print("EXTENSION FREE LOGS EXC", exc, flush=True)

            if event["eventType"] == "INVOKE":
                for _file in tmp.walkfiles("core.chr*"):
                    _file.remove_p()
            elif event["eventType"] == "SHUTDOWN":
                # Wait for 1 sec to receive remaining events
                time.sleep(1)

                return
    except Exception as exc:
        print("EXTENSION CRASH", flush=True)
        print(exc, flush=True)


def main():
    extension_id = register_extension()
    process_events(extension_id)


if __name__ == "__main__":
    main()
