FROM amazon/aws-lambda-python:3.11

ARG AWS_ACCESS_KEY_ID
ARG AWS_SECRET_ACCESS_KEY
ARG DOMAIN
ARG REPOSITORY
ARG DOMAIN_OWNER
ARG AWS_REGION

RUN yum update -y && \
    yum install -y gcc ca-certificates && \
    yum clean all

COPY ./requirements /opt/compose/requirements

RUN pip3 install -r /opt/compose/requirements/prod.txt

RUN pip3 install awscli

RUN mkdir /opt/code

WORKDIR /opt/code

COPY ./isolated_proxy-0.0.1-py2.py3-none-any.whl /opt/compose/isolated_proxy-0.0.1-py2.py3-none-any.whl

RUN pip3 install /opt/compose/isolated_proxy-0.0.1-py2.py3-none-any.whl --break-system-packages

RUN rm -rf /root/.cache/*

COPY ./entrypoint.sh /opt/compose/entrypoint.sh

COPY --from=public.ecr.aws/sentry/sentry-python-serverless-sdk:6 /opt/python /var/lang/lib/python3.11/site-packages

COPY ./GeoLite2-Country.mmdb /opt/
COPY ./GeoLite2-City.mmdb /opt/

ENV REQUESTS_CA_BUNDLE=/etc/pki/tls/certs/ca-bundle.crt

ENTRYPOINT ["/opt/compose/entrypoint.sh"]
