import logging
import time
import typing as t

from requests import Response

from isolated_proxy import cfg, inj

brighdata_session = inj["brightdata_session"]
logger = logging.getLogger(__name__)


def refresh(zone_name, ips: t.List[str], country="us"):
    data = {"zone": zone_name, "ips": ips, "country": country}

    return brighdata_session.post("https://api.brightdata.com/zone/ips/refresh", json=data)


def add_ip(zone_name, count: int, country="us"):
    if cfg.debug:
        response = Response()
        response._content = b'{"ips":["***********","***********"]}'  # noqa
        response.status_code = 200
        return response

    data = {"customer": cfg.proxy_customer, "zone": zone_name, "count": count, "country": country}
    response = brighdata_session.post("https://api.brightdata.com/zone/ips", json=data)
    if response.status_code != 200:
        mag = "Failed ips adding request: %(content)s"
        context = {"content": response.content.decode()}
        logger.error(mag, context)
        raise Exception("Can't add ips")

    new_ips = response.json()["ips"]
    if len(new_ips) < 2:  # sometimes we could get something like "wait, we can't give you more ips"
        time.sleep(5)
        response = brighdata_session.post("https://api.brightdata.com/zone/ips", json=data)
        if response.status_code != 200:
            mag = "Failed ips adding request: %(content)s"
            context = {"content": response.content.decode()}
            logger.error(mag, context)
            raise Exception("Can't add ips")

    return response
