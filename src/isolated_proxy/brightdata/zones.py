from requests import Response
from yarl import URL

from isolated_proxy import cfg, inj

brighdata_session = inj["brightdata_session"]


def create(zone_name):
    if cfg.debug:
        response = Response()
        response._content = b'{"zone":{"created":"2024-08-12T15:28:57.719Z","password":["5bpkll3bfxy0"],"ips":"any","plan":{"start":"2024-08-12T15:28:57.734Z","type":"static","ips_type":"dedicated","bandwidth":"payperusage","product":"dc","disable":"noips"}},"plan":{"start":"2024-08-12T15:28:57.734Z","type":"static","ips_type":"dedicated","bandwidth":"payperusage","product":"dc","disable":"noips"},"alloc_opt":{"alloc_max_available":false,"no_reserve":false,"user":"basic/<EMAIL>","req_source":"api"}}'  # noqa
        response.status_code = 200
        return response

    data = {
        "zone": {"name": zone_name},
        "plan": {
            "type": "static",  # static|resident
            "ips_type": "selective",  # shared|dedicated|selective
            "ip_alloc_preset": "shared_block",
            "bandwidth": "unlimited",  # payperusage|unlimited
            "mobile": False,
            "city": False,
            "asn": False,
            "vip": False,
            # 'vips_type': 'shared',  # shared|vip|domain
            # 'vips': 0,
            "domain_whitelist": "brightdata.com doordash.com grubhub.com lumtest.com myip.com ubereats.com",
            "not_country": None,
            "vip_country": "any",
            "exclusive_sec": 0,
            "exclusive_type": "days",
            "exclusive_num": 0,
            "ips": 2,
        },
    }

    return brighdata_session.post(
        "https://api.brightdata.com/zone",
        json=data,
    )


def get(zone_name):
    url = URL("https://api.brightdata.com/zone")
    url = url.with_query({"zone": zone_name})

    return brighdata_session.get(url)


def get_active_zones():
    url = "https://api.brightdata.com/zone/get_active_zones"

    return brighdata_session.get(url).json()


def get_blocklisted_ips(zone_name):
    return brighdata_session.get(f"https://api.brightdata.com/zone/blacklist?zones={zone_name}")


def delete_blocklisted_ip(zone_name, ip):
    data = {"zone": zone_name, "ip": ip}
    return brighdata_session.delete("https://api.brightdata.com/zone/blacklist", data=data)


def status(zone_name):
    url = URL("https://api.brightdata.com/zone/status")
    url = url.with_query({"zone": zone_name})

    return brighdata_session.get(str(url))


def delete(zone_name):
    data = {"zone": zone_name}
    return brighdata_session.delete("https://api.brightdata.com/zone", json=data)


def passwords(zone_name):
    url = URL("https://api.brightdata.com/zone/passwords")
    url = url.with_query({"zone": zone_name})

    return brighdata_session.get(str(url))


def switch_100_uptime(zone_name):
    url = "https://api.brightdata.com/zone/switch_100uptime"

    payload = {"zone": zone_name, "active": 100}
    return brighdata_session.post(url, json=payload)


def update_zone(zone_name, disable):
    """0 - activate 100%, 1 - disable 100%"""

    data = {"zone": zone_name, "disable": disable}

    return brighdata_session.post("https://api.brightdata.com/zone/change_disable", data=data)


def zone_stats(zone_name):
    url = URL("https://api.brightdata.com/zone/ips?")
    url = url.with_query({"zone": zone_name})

    return brighdata_session.get(str(url))
