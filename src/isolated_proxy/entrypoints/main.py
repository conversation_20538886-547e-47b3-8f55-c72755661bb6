import click


@click.group()
@click.pass_context
def main(ctx):
    pass


@main.command(name="migrate_pg")
@click.pass_context
def migrate_pg(ctx):
    from .migrate_pg import entry

    options = {
        "ctx": ctx,
    }
    entry(options=options)


@main.command(name="smart_proxy_checker")
@click.pass_context
def smart_proxy_checker(ctx):
    from .smart_proxy_checker import entry

    options = {
        "ctx": ctx,
    }
    entry(options=options)


cli = click.CommandCollection(sources=[main])
