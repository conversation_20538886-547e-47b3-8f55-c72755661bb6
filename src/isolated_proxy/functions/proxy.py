import logging

import sentry_sdk

from isolated_proxy import cfg, inj
from isolated_proxy.decorators import set_log_context
from isolated_proxy.enums import PoolStatus, ProxyRequest, ProxyStrategyEnum
from isolated_proxy.functions.abc import ProxyStrategy
from isolated_proxy.handlers.proxy_handler import MultiProxyRotator, SingleProxyRotator

logger = logging.getLogger(__name__)
sqs_client = inj["sqs_client"]
executor = inj["executor"]
storage = inj["pg_storage"]


def get_proxy_strategy():
    if cfg.logic == ProxyStrategyEnum.single_pool.value:
        return SinglePoolProxyStrategy()
    else:
        return MultiPoolProxyStrategy()


class SinglePoolProxyStrategy(ProxyStrategy):
    @classmethod
    @set_log_context
    def set_proxies(cls, event, context):
        with sentry_sdk.configure_scope() as scope:
            rotator = SingleProxyRotator()
            rotator.set_proxies()

            scope.clear()

    @classmethod
    @set_log_context
    def get_proxy(cls, event: ProxyRequest, context):
        msg = "Getting proxy in SinglePoolProxyStrategy with event: %(event)s"
        context = {"event": event}
        logger.info(msg, context)

        response = SingleProxyRotator().get_proxy(
            {
                "value": event["value"],
                "integration_type": event["integration_type"],
                "retry_counter": event.get("retry_counter", 0),
                "project_type": event["project_type"],
                "exclude": event.get("exclude", []),
                "location_count": event.get("location_count", 0),
                "residential": event.get("residential", False),
                "residential_type": event.get("residential_type", None),
            },
        )

        return {
            "statusCode": 200,
            "headers": {"Content-Type": "application/json"},
            "body": response,
        }

    @classmethod
    @set_log_context
    def block_proxy(cls, event, context):
        msg = "Blocking proxy in SinglePoolProxyStrategy with event: %(event)s"
        context = {"event": event}
        logger.info(msg, context)

        response = SingleProxyRotator().block_proxy(
            {
                "value": event["value"],
                "ips": event["ips"],
                "integration_type": event["integration_type"],
                "project_type": event["project_type"],
            },
        )

        return {
            "statusCode": 200,
            "headers": {"Content-Type": "application/json"},
            "body": response,
        }

    @classmethod
    def update_pool_status(cls, event, context):
        raise NotImplementedError("update_pool_status is not implemented for SinglePoolProxyStrategy")


class MultiPoolProxyStrategy(ProxyStrategy):
    @classmethod
    @set_log_context
    def get_proxy(cls, event: ProxyRequest, context):
        msg = "Getting proxy in MultiPoolProxyStrategy with event: %(event)s"
        context = {"event": event}
        logger.info(msg, context)

        chain_id = event.get("value")
        assert chain_id is not None, "chain_id is required"

        response = MultiProxyRotator(chain_id=chain_id, location_count=event.get("location_count", 0)).get_proxy(event)

        return {
            "statusCode": 200,
            "headers": {"Content-Type": "application/json"},
            "body": response,
        }

    @classmethod
    @set_log_context
    def block_proxy(cls, event, context):
        msg = "Blocking proxy in MultiPoolProxyStrategy with event: %(event)s"
        context = {"event": event}
        logger.info(msg, context)

        assert event.get("value") is not None, "value is required"
        assert event.get("ips") is not None, "ips are required"

        response = MultiProxyRotator(chain_id=event["value"], location_count=0).refresh_proxy_ip(
            _ips=event["ips"],
        )

        return {
            "statusCode": 200,
            "headers": {"Content-Type": "application/json"},
            "body": response,
        }

    @classmethod
    @set_log_context
    def update_pool_status(cls, event, context):
        msg = "Updating pool status in MultiPoolProxyStrategy with event: %(event)s"
        context = {"event": event}
        logger.info(msg, context)

        assert event.get("new_status") is not None, "disable is required"
        assert event.get("new_status") in [PoolStatus.disabled.value, PoolStatus.active.value], "new_status must be either ACTIVE or INACTIVE"

        response = MultiProxyRotator(chain_id=event["chain_id"], location_count=0).update_pool_status(event["new_status"])

        return {
            "statusCode": 200,
            "headers": {"Content-Type": "application/json"},
            "body": response,
        }

    @classmethod
    @set_log_context
    def set_proxies(cls, event, context):
        raise NotImplementedError("set_proxies is not implemented for MultiPoolProxyStrategy")


def set_proxies(event, context):
    return get_proxy_strategy().set_proxies(event, context)


def get_proxy(event: ProxyRequest, context):
    return get_proxy_strategy().get_proxy(event, context)


def update_pool_status(event, context):
    return get_proxy_strategy().update_pool_status(event, context)


def block_proxy(event, context):
    return get_proxy_strategy().block_proxy(event, context)


set_proxies(None, None)
