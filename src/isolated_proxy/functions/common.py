import datetime
import logging
from importlib import import_module
from pathlib import Path

from alembic import command
from alembic.config import Config

from isolated_proxy import cfg, inj
from isolated_proxy.handlers.slack_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SlackIsolatedLoginErrors

logger = logging.getLogger(__name__)
storage = inj["pg_storage"]


def pg_migrate(event, context):
    project_root = Path(import_module("isolated_proxy").__path__[0]).absolute()

    alembic_cfg = Config(project_root / "assets" / "alembic.ini")

    with inj["pg"].begin() as tr:
        alembic_cfg.attributes["connection"] = tr
        command.upgrade(alembic_cfg, "head")


def check_balance(event, context):
    from isolated_proxy.brightdata import account

    response = account.account_balance().json()

    balance, pending_costs = response.get("balance"), response.get("pending_costs", 0)

    if balance is None or pending_costs is None:
        raise Exception("Can't get balance")

    remaining = balance - pending_costs
    logger.info("Balance on proxy service: %s", remaining)
    if remaining < cfg.min_balance_brightdata:  # noqa
        SlackHandler().send_message(
            message=f"{cfg.slack.tag_user_id} Balance on BrightDataproxy service is low: {round(remaining, 2)}. \nPlease, refill the balance.",
            channel=cfg.slack.channel_dev_notifications,
        )


def smart_proxy_checker(event, context):
    from isolated_proxy.handlers.smartproxy.checker import BalanceCheckerManager

    session_details_victor = storage.smart_proxy_session.get(username="<EMAIL>").first()
    session_details_victor["name"] = "smartproxy"
    session_details_v = storage.smart_proxy_session.get(username="<EMAIL>").first()
    session_details_v["name"] = "atlas"

    if not session_details_v or not session_details_victor:
        raise Exception("No session details found in DB")

    for session_details in [session_details_victor, session_details_v]:
        manager = BalanceCheckerManager(session_details, slack_handler=SlackIsolatedLoginErrors(), config=cfg, s3_client=None)
        session_details = manager.start()

        if session_details:
            session_details.pop("name", None)
            storage.smart_proxy_session.upsert(**session_details)


def smart_proxy_request_checker(event, context):
    from isolated_proxy.handlers.smartproxy.requests_checker import (
        RequestCheckerManager,
    )

    manager = RequestCheckerManager(slack_handler=SlackIsolatedLoginErrors(), cfg=cfg, storage=storage)
    manager.start()


def blocklisted_cleaner(event, context):
    storage.blocklisted_ips.delete(expiring_at__lt=datetime.datetime.utcnow())
