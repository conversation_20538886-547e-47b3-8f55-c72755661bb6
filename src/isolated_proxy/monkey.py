import os

__patched = False


def patch_requests_json():
    import json

    from plutus_core import utils
    from requests import models

    class JsonModule:
        # TODO: fix fallback
        def dumps(self, *args, **kwargs):
            try:
                if "allow_nan" in kwargs:
                    kwargs.pop("allow_nan")
                return utils.dumps(*args, **kwargs)
            except Exception:
                return json.dumps(*args)

        def loads(self, *args, **kwargs):
            try:
                return utils.loads(*args, **kwargs)
            except Exception:
                return json.loads(*args)

    json_module = JsonModule()

    models.complexjson = json_module


def patch_httpx_json():
    import json

    from httpx import _content
    from plutus_core import utils

    class JsonModule:
        # TODO: fix fallback
        def dumps(self, *args, **kwargs):
            try:
                if "allow_nan" in kwargs:
                    kwargs.pop("allow_nan")
                return utils.dumps(*args, **kwargs)
            except Exception:
                return json.dumps(*args)

        def loads(self, *args, **kwargs):
            try:
                return utils.loads(*args, **kwargs)
            except Exception:
                return json.loads(*args)

    json_module = JsonModule()

    _content.json_dumps = json_module.dumps


def patch_selenium_undetected():
    import sys

    import undetected_chromedriver
    from undetected_chromedriver import patcher

    class PatchedPatcher(patcher.Patcher):
        platform = sys.platform

        if platform.endswith("win32"):
            d = "~/appdata/roaming/undetected_chromedriver"
        elif "AWS_LAMBDA_RUNTIME_API" in os.environ:
            d = "/tmp/undetected_chromedriver"
        elif platform.startswith("linux"):
            d = "~/.local/share/undetected_chromedriver"
        elif platform.endswith("darwin"):
            d = "~/Library/Application Support/undetected_chromedriver"
        else:
            d = "~/.undetected_chromedriver"

        data_path = os.path.abspath(os.path.expanduser(d))

    undetected_chromedriver.Patcher = PatchedPatcher


def patch_multiprocessing():
    import multiprocessing
    from threading import Lock

    multiprocessing.Lock = Lock


def patch_all():
    global __patched
    if not __patched:
        patch_multiprocessing()
        patch_requests_json()
        patch_httpx_json()

        if os.environ.get("ISOLATED_PROXY_SELENIUM_LAMBDA"):
            patch_selenium_undetected()

        __patched = True
