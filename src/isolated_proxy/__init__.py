import os

if os.getenv("ISOLATED_PROXY_SELENIUM_LAMBDA"):
    from isolated_proxy.monkey import patch_all  # noqa

    patch_all()

from isolated_proxy.app import make_all  # noqa

cfg, inj = make_all()

import logging  # noqa

if not cfg.debug:
    import sentry_sdk  # noqa
    from sentry_sdk.integrations.logging import LoggingIntegration  # noqa

    sentry_logging = LoggingIntegration(
        level=logging.WARNING,
        event_level=logging.WARNING,
    )
    sentry_sdk.init(  # type: ignore
        cfg.sentry.url,
        traces_sample_rate=0,
        default_integrations=False,
        integrations=[sentry_logging],
    )

if len(logging.getLogger().handlers) > 0:
    # lambda
    logging.getLogger().setLevel(cfg.logging_level)
else:
    # local
    logging.basicConfig(level=cfg.logging_level)

__version__ = "0.0.1"
