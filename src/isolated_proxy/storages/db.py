import datetime
import typing as t

import injections
import sqlalchemy as sa
from sqlalchemy import cast, desc
from sqlalchemy import update as sql_update
from sqlalchemy.dialects import postgresql
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.engine.base import NestedTransaction
from sqlalchemy.sql.elements import ClauseElement, ColumnClause

from isolated_proxy.db import models as dispute_models
from isolated_proxy.storages import abc


@injections.has
class PGStorage(abc.BaseStorage):
    def __init__(self, pg, models=None):
        self.pg = pg

        if models is None:
            self.models = dispute_models
        else:
            self.models = models

    def execute(self, stmt, *, tr):
        if isinstance(tr, NestedTransaction):
            return tr.connection.execute(stmt)

        return tr.execute(stmt)

    # def __injected__(self):
    #     # TODO: validate latest migration
    #     pass

    def __getattr__(self, name: str) -> "DBModelStorage":
        model = getattr(self.models, name)

        if not isinstance(model, sa.Table):
            model = model.__table__

        return DBModelStorage(model, self.pg, self.models)


class ModelProxy:
    def __init__(self, result: t.Optional[t.List[t.Dict[str, t.Any]]]):
        if result is None:
            self.result: t.List[t.Dict[str, t.Any]] = []
        else:
            self.result = result

    def first(self) -> t.Optional[t.Dict[str, t.Any]]:
        return self.result[0] if self.result else None

    def last(self) -> t.Optional[t.Dict[str, t.Any]]:
        return self.result[-1] if self.result else None

    def __getitem__(self, i: int) -> t.Dict[str, t.Any]:
        return self.result[i]

    def __len__(self) -> int:
        return len(self.result)

    def __iter__(self) -> t.Generator[t.Dict[str, t.Any], None, None]:
        yield from self.result

    def __repr__(self) -> str:
        return str(self.result)

    def __bool__(self) -> bool:
        return bool(self.result)


class DBModelStorage:
    join_stmt = None

    suffixes = ["gte", "gt", "lte", "lt", "ne", "in", "not_in", "contains"]

    def __init__(self, model: sa.Table, pg: sa.engine.Engine, models=None):
        self.model = model
        self.pg = pg

        if models is None:
            self.models = dispute_models
        else:
            self.models = models

    def check_is_array(self, key: str, model: sa.Table) -> ColumnClause:
        col = t.cast(sa.Column, getattr(model.c, key))

        if col.type.__class__.__name__ == "ARRAY":
            return t.cast(ColumnClause, sa.func.cardinality(col))

        return col

    def filters(self, stmt: sa.sql.Select, params: t.Dict[str, t.Any]) -> sa.sql.Select:

        for key, value in params.items():
            lookups = key.split("__")

            prefix = key = suffix = None

            if len(lookups) == 3:
                prefix, key, suffix = lookups
            elif len(lookups) == 2:
                if lookups[-1] in self.suffixes:
                    key, suffix = lookups
                else:
                    prefix, key = lookups
            elif len(lookups) == 1:
                key = lookups[0]

            if suffix is not None and suffix not in self.suffixes:
                raise NotImplementedError(f"Unsupported lookup: {suffix}")

            if not prefix:
                model = self.model
            else:
                model = t.cast(sa.Table, getattr(self.models, prefix))

            if prefix and self.join_stmt is None:
                raise NotImplementedError("You can not use nested lookups without join statement")

            if suffix == "gte":
                stmt = stmt.where(self.check_is_array(key, model) >= value)
            elif suffix == "gt":
                stmt = stmt.where(self.check_is_array(key, model) > value)
            elif suffix == "lte":
                stmt = stmt.where(self.check_is_array(key, model) <= value)
            elif suffix == "lt":
                stmt = stmt.where(self.check_is_array(key, model) < value)
            elif suffix == "ne":
                stmt = stmt.where(self.check_is_array(key, model) != value)
            elif suffix == "in":
                stmt = stmt.where(getattr(model.c, key).in_(value))
            elif suffix == "not_in":
                stmt = stmt.where(getattr(model.c, key).notin_(value))
            elif suffix == "contains":
                stmt = stmt.where(getattr(model.c, key).contains(cast(value, postgresql.ARRAY(postgresql.VARCHAR))))
            else:
                stmt = stmt.where(getattr(model.c, key) == value)

        return stmt

    def execute(self, stmt: t.Union[str, ClauseElement], tr: t.Optional[sa.engine.Connection]) -> sa.engine.CursorResult:
        if tr:
            return tr.execute(stmt)

        with self.pg.begin() as tr:
            return tr.execute(stmt)

    def create(self, tr: t.Optional[sa.engine.Connection] = None, **params: t.Dict[str, t.Any]) -> None:
        stmt = insert(self.model).values(**params)

        self.execute(stmt, tr)

    def update(self, *, where: t.Dict[str, t.Any], tr: t.Optional[sa.engine.Connection] = None, params: t.Dict[str, t.Any]) -> None:
        stmt = sql_update(self.model).where(*where).values(**params)

        self.execute(stmt, tr)

    def upsert(self, tr: t.Optional[sa.engine.Connection] = None, **params: t.Dict[str, t.Any]) -> None:
        try:
            updated_at = self.model.c.updated_at.name
        except AttributeError:
            updated_at = self.model.c.updated.name

        params.pop(updated_at, None)

        stmt = insert(self.model).values(**params)

        set_ = {k: stmt.excluded[k] for k in params}
        set_[updated_at] = datetime.datetime.now(datetime.timezone.utc)

        stmt = stmt.on_conflict_do_update(
            constraint=self.model.primary_key,
            set_=set_,
        )

        self.execute(stmt, tr)

    def get(
        self,
        tr: t.Optional[sa.engine.Connection] = None,
        select_for_update: bool = False,
        limit: t.Optional[int] = None,
        _columns: t.Optional[t.List[str]] = None,
        **params: t.Dict[str, t.Any],
    ) -> ModelProxy:

        if _columns is not None:
            _columns = [getattr(self.model.c, column) for column in _columns]
        else:
            _columns = self.model.c

        if self.join_stmt is not None:
            joined_prefix = f"{self.join_stmt.right.name}___"

            stmt = sa.select(
                *[
                    *[col.label(f"{joined_prefix}{col.name}") for col in self.join_stmt.right.c],
                    *_columns,
                ],
            )
            stmt = stmt.select_from(self.join_stmt)
        else:
            if isinstance(_columns, list):
                stmt = sa.select(*_columns)
            else:
                stmt = sa.select(_columns)

        stmt = self.filters(stmt, params)

        if hasattr(self.model.c, "created_at"):
            created_at = self.model.c.created_at.name
        elif hasattr(self.model.c, "created"):
            created_at = self.model.c.created.name
        else:
            created_at = None

        if created_at:
            stmt = stmt.order_by(desc(created_at))

        if limit:
            stmt = stmt.limit(limit)

        if select_for_update:
            ret_ = self.execute(stmt.with_for_update(), tr)
        else:
            ret_ = self.execute(stmt, tr)

        ret = [obj._asdict() for obj in ret_]

        if self.join_stmt is not None:
            new_ret = []

            for item in ret:
                _item = {self.join_stmt.right.name: {}}

                for key, value in item.items():
                    if key.startswith(joined_prefix):
                        _item[self.join_stmt.right.name][key[len(joined_prefix) :]] = value  # noqa
                    else:
                        _item[key] = value

                new_ret.append(_item)

            ret = new_ret
            self.join_stmt = None

        return ModelProxy(ret)

    def delete(self, tr: t.Optional[sa.engine.Connection] = None, **params: t.Dict[str, t.Any]) -> None:
        stmt = sa.delete(self.model)
        stmt = self.filters(stmt, params)

        self.execute(stmt, tr)

    def delete_all(self, tr: t.Optional[sa.engine.Connection] = None) -> None:
        stmt = sa.delete(self.model)

        self.execute(stmt, tr)

    def bulk_upsert(self, values: t.List[t.Dict[str, t.Any]], tr: t.Optional[sa.engine.Connection] = None) -> None:
        if not values:
            return

        if not tr:
            with self.pg.begin() as tr:
                self._bulk_upsert(values, tr)
                return

        self._bulk_upsert(values, tr)

    def _bulk_upsert(self, values: t.List[t.Dict[str, t.Any]], tr: sa.engine.Connection) -> None:
        self.__bulk_upsert(values, tr)

    def __bulk_upsert(self, values: t.List[t.Dict[str, t.Any]], tr: sa.engine.Connection) -> None:
        if not values:
            return

        if hasattr(self.model.c, "updated_at"):
            updated_at = self.model.c.updated_at.name
        elif hasattr(self.model.c, "updated"):
            updated_at = self.model.c.updated.name
        else:
            updated_at = None

        stmt = insert(self.model).values(values)

        set_ = {k: stmt.excluded[k] for k in values[0]}

        if updated_at:
            set_[updated_at] = datetime.datetime.now(datetime.timezone.utc)

        stmt = stmt.on_conflict_do_update(
            constraint=self.model.primary_key,
            set_=set_,
        )

        self.execute(stmt, tr)

    def join(self, right_model: sa.Table, ids: t.Optional[t.List[str]] = None) -> "DBModelStorage":
        onclause = None
        if ids is not None:
            onclause = getattr(self.model.c, ids[0]) == getattr(right_model.c, ids[1])

        self.join_stmt = self.model.join(right_model, onclause=onclause)

        return self
