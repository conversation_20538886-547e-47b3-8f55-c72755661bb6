import logging
from functools import wraps

import sentry_sdk

logger = logging.getLogger(__name__)


def set_log_context(fn):
    def wrapper(fn):
        @wraps(fn)
        def wrapped(*args, **kwargs):
            with sentry_sdk.configure_scope() as scope:
                try:
                    return fn(*args, **kwargs)
                except Exception as exc:
                    logger.critical(exc, exc_info=exc)
                    raise exc
                finally:
                    scope.clear()

                    client = sentry_sdk.Hub.current.client

                    if client is not None:
                        client.flush()

        return wrapped

    if fn is None:
        return wrapper

    if callable(fn):
        return wrapper(fn)

    raise NotImplementedError
