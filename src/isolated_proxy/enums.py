import typing as t
from enum import Enum


class CustomEnum(Enum):
    @classmethod
    def names(cls):
        return tuple(item.name for item in cls)

    @classmethod
    def values(cls):
        return tuple(item.value for item in cls)

    @classmethod
    def choices(cls):
        return tuple((item.value, item.name) for item in cls)

    @classmethod
    def choices_reversed(cls):
        return {v: k for k, v in cls.choices()}


class IntegrationType(CustomEnum):
    ue = "ubereats"
    dd = "doordash"
    gh = "grubhub"
    sd = "skip_the_dishes"

    ubereats = "ue"
    doordash = "dd"
    grubhub = "gh"
    skip_the_dishes = "sd"


class ProxyExplicit(t.TypedDict):
    server: str
    username: str
    password: str


class GeoData(t.TypedDict):
    timezone_id: str
    longitude: float
    latitude: float


class ProxyData(t.TypedDict):
    ip: str | None
    proxy: str
    pw_proxy: ProxyExplicit
    timezone_id: str
    longitude: float
    latitude: float


class ProxyRequest(t.TypedDict):
    value: str
    retry_counter: t.Optional[int]
    exclude: t.Optional[t.List[str]]
    integration_type: str
    project_type: t.Optional[str]
    location_count: t.Optional[int]
    residential: t.Optional[bool]
    residential_type: t.Optional[str]


class BlockProxyRequest(t.TypedDict):
    ips: t.List[str]
    value: str
    integration_type: str
    project_type: t.Optional[str]


class IpParamsMulti(t.TypedDict):
    ip: str
    proxy_data: ProxyData


class IpParamsSingle(t.TypedDict):
    ip: str
    proxy: str


class ProxyDBData(t.TypedDict):
    pool_name: str
    api_token: str
    host: str
    password: str
    username: str


class PoolStatus(CustomEnum):
    active = "ACTIVE"
    disabled = "DISABLED"


class ProxyStrategyEnum(CustomEnum):
    single_pool = "single_pool"
    multi_pool = "multi_pool"


class ProjectType(CustomEnum):
    dispute = "dispute"
    plutus = "plutus"
