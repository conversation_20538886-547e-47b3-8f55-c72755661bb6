import datetime

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from isolated_proxy.db.models._meta import meta

ips = sa.Table(
    "ips",
    meta,
    sa.Column(
        "pool_id",
        postgresql.VARCHAR(36),
        sa.<PERSON>(
            "proxy_pools.pool_id",
            ondelete="CASCADE",
        ),
        primary_key=True,
    ),
    sa.Column(
        "ip",
        sa.String,
        primary_key=True,
    ),
    sa.Column(
        "created_at",
        sa.DateTime(timezone=True),
        default=datetime.datetime.utcnow,
    ),
    sa.<PERSON>umn(
        "updated_at",
        sa.DateTime(timezone=True),
        onupdate=datetime.datetime.utcnow,
    ),
    sa.Column(
        "disabled",
        sa.<PERSON>,
        default=False,
        server_default="f",
    ),
)
