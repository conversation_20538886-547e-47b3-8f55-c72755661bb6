import datetime

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from isolated_proxy.db.models._meta import meta

blocklisted_ips = sa.Table(
    "blocklisted_ips",
    meta,
    sa.Column(
        "pool_id",
        postgresql.VARCHAR(36),
        sa.<PERSON>(
            "proxy_pools.pool_id",
            ondelete="CASCADE",
        ),
        primary_key=True,
    ),
    sa.Column(
        "value",
        postgresql.VARCHAR(36),
        primary_key=True,
    ),
    sa.<PERSON>umn(
        "ip",
        postgresql.VARCHAR(36),
        primary_key=True,
    ),
    sa.Column(
        "integration_id",
        postgresql.VARCHAR,
        primary_key=True,
    ),
    sa.Column(
        "created_at",
        sa.DateTime(timezone=True),
        default=datetime.datetime.utcnow,
    ),
    sa.Column(
        "updated_at",
        sa.DateTime(timezone=True),
        onupdate=datetime.datetime.utcnow,
    ),
    sa.Column(
        "expiring_at",
        sa.DateTime(timezone=True),
        onupdate=datetime.datetime.utcnow,
    ),
)
