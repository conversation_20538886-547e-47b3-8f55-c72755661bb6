import datetime

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from isolated_proxy.db.models._meta import meta

smart_proxy_session = sa.Table(
    "smart_proxy_session",
    meta,
    sa.Column(
        "username",
        postgresql.VARCHAR(36),
        primary_key=True,
    ),
    sa.Column(
        "password",
        postgresql.VARCHAR(36),
    ),
    sa.<PERSON>umn(
        "created_at",
        sa.DateTime(timezone=True),
        default=datetime.datetime.utcnow,
    ),
    sa.<PERSON>umn(
        "updated_at",
        sa.DateTime(timezone=True),
        onupdate=datetime.datetime.utcnow,
    ),
    sa.Column(
        "local_storage",
        postgresql.JSONB,
    ),
    sa.Column(
        "cookies",
        postgresql.JSONB,
    ),
    sa.Column(
        "session_storage",
        postgresql.JSONB,
    ),
    sa.Column(
        "expiring_at",
        sa.DateTime(timezone=True),
    ),
)
