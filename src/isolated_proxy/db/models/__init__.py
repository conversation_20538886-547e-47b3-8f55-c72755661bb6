from importlib import import_module
from pathlib import Path

from isolated_proxy.db.models._meta import meta

here = Path(__path__[0])  # noqa

modules = {
    module: getattr(
        import_module(".".join((__package__, module))),
        module,
    )
    for module in [str(module.name)[:-3] for module in here.glob("*.py") if not str(module.name).startswith("_")]
}

globals().update(modules)

__all__ = (
    "meta",
    *modules,
)
