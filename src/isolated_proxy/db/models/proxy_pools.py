import datetime

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from isolated_proxy.db.models._meta import meta

proxy_pools = sa.Table(
    "proxy_pools",
    meta,
    sa.Column(
        "pool_id",
        postgresql.VARCHAR(36),
        primary_key=True,
    ),
    sa.Column(
        "password",
        sa.String,
        nullable=False,
    ),
    sa.Column(
        "username",
        sa.String,
        nullable=False,
    ),
    sa.Column(
        "created_at",
        sa.DateTime(timezone=True),
        default=datetime.datetime.utcnow,
    ),
    sa.Column(
        "updated_at",
        sa.DateTime(timezone=True),
        onupdate=datetime.datetime.utcnow,
    ),
    sa.Column(
        "status",
        sa.String,
    ),
    sa.Column(
        "default_proxy",
        sa.Boolean,
        default=False,
        server_default=sa.false(),
    ),
    sa.Column(
        "project_name",
        sa.String,
    ),
    sa.Column(
        "residential",
        sa.<PERSON>,
        default=False,
        server_default=sa.false(),
    ),
)
