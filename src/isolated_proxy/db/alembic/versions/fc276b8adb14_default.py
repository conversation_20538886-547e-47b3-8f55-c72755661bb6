"""disabled
 Revision ID: fc276b8adb14
 Revises: 1aa7ccdad14b
 Create Date: 2025-02-03 10:53:20.785499
 """
from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "fc276b8adb14"
down_revision: Union[str, None] = "1aa7ccdad14b"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("ips", sa.Column("disabled", sa.<PERSON>(), server_default="f", nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("ips", "disabled")
    # ### end Alembic commands ###
