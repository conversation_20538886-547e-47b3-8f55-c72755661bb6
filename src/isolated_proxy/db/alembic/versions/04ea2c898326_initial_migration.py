"""Initial migration

Revision ID: 04ea2c898326
Revises:
Create Date: 2024-08-14 15:37:12.773277

"""
from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "04ea2c898326"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "proxy_pools",
        sa.Column("pool_id", sa.VARCHAR(length=36), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("pool_id"),
    )
    op.create_table(
        "ips",
        sa.Column("pool_id", sa.VARCHAR(length=36), nullable=False),
        sa.Column("ip", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(["pool_id"], ["proxy_pools.pool_id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("pool_id", "ip"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("ips")
    op.drop_table("proxy_pools")
    # ### end Alembic commands ###
