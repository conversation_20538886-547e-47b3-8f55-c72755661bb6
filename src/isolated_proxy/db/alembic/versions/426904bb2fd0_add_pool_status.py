"""Add pool status

Revision ID: 426904bb2fd0
Revises: 0ed54d60ef65
Create Date: 2024-08-19 18:50:59.624378

"""
from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "426904bb2fd0"
down_revision: Union[str, None] = "0ed54d60ef65"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("proxy_pools", sa.Column("status", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("proxy_pools", "status")
    # ### end Alembic commands ###
