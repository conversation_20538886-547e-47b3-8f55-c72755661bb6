"""Changed proxy_pools

Revision ID: 4dd32b4463ce
Revises: 0986e9765ac1
Create Date: 2025-02-25 16:27:05.238496

"""
from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "4dd32b4463ce"
down_revision: Union[str, None] = "0986e9765ac1"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("proxy_pools", sa.Column("project_name", sa.String(), nullable=True))
    op.add_column("proxy_pools", sa.Column("residential", sa.Boolean(), server_default=sa.text("false"), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("proxy_pools", "residential")
    op.drop_column("proxy_pools", "project_name")
    # ### end Alembic commands ###
