"""pool_default

Revision ID: 0986e9765ac1
Revises: fc276b8adb14
Create Date: 2025-02-21 18:42:34.465196

"""
from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "0986e9765ac1"
down_revision: Union[str, None] = "fc276b8adb14"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("proxy_pools", sa.Column("default_proxy", sa.<PERSON>(), server_default=sa.text("false"), nullable=True))
    op.execute(
        """
           CREATE UNIQUE INDEX unique_default_proxy ON proxy_pools (default_proxy)
           WHERE default_proxy = TRUE;
       """,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("DROP INDEX IF EXISTS unique_default_proxy;")
    op.drop_column("proxy_pools", "default_proxy")
    # ### end Alembic commands ###
