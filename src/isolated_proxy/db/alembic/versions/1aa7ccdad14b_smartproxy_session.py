"""SmartProxy session
 Revision ID: 1aa7ccdad14b
 Revises: cc6e9a1d233e
 Create Date: 2025-01-08 01:41:56.655949
 """
from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "1aa7ccdad14b"
down_revision: Union[str, None] = "cc6e9a1d233e"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "smart_proxy_session",
        sa.Column("username", sa.VARCHAR(length=36), nullable=False),
        sa.Column("password", sa.VARCHAR(length=36), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("local_storage", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("cookies", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("session_storage", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("expiring_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("username"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("smart_proxy_session")
    # ### end Alembic commands ###
