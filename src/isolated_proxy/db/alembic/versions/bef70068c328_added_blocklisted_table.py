"""Added blocklisted table

Revision ID: bef70068c328
Revises: 426904bb2fd0
Create Date: 2024-09-10 14:45:42.839780

"""
from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "bef70068c328"
down_revision: Union[str, None] = "426904bb2fd0"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "blocklisted_ips",
        sa.Column("value", sa.VARCHAR(length=36), nullable=False),
        sa.Column("ip", sa.VARCHAR(length=36), nullable=False),
        sa.Column("integration_id", sa.VARCHAR(), nullable=False),
        sa.<PERSON>umn("created_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("expiring_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("value", "ip", "integration_id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("blocklisted_ips")
    # ### end Alembic commands ###
