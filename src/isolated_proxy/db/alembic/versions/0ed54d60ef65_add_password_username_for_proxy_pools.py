"""Add password, username for proxy pools

Revision ID: 0ed54d60ef65
Revises: 04ea2c898326
Create Date: 2024-08-15 20:47:39.980812

"""
from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "0ed54d60ef65"
down_revision: Union[str, None] = "04ea2c898326"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("proxy_pools", sa.Column("password", sa.String(), nullable=False))
    op.add_column("proxy_pools", sa.Column("username", sa.String(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("proxy_pools", "username")
    op.drop_column("proxy_pools", "password")
    # ### end Alembic commands ###
