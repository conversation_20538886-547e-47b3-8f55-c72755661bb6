"""Added pool id to blocklisted table

Revision ID: cc6e9a1d233e
Revises: bef70068c328
Create Date: 2024-09-16 17:21:26.936457

"""
from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "cc6e9a1d233e"
down_revision: Union[str, None] = "bef70068c328"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("blocklisted_ips", sa.Column("pool_id", sa.VARCHAR(length=36), nullable=False))
    op.drop_constraint("blocklisted_ips_pkey", "blocklisted_ips", type_="foreignkey")
    op.create_unique_constraint("blocklisted_ips_pkey", "blocklisted_ips", ["pool_id", "value", "ip", "integration_id"])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("blocklisted_ips_pkey", "blocklisted_ips", type_="foreignkey")
    op.create_unique_constraint("blocklisted_ips_pkey", "blocklisted_ips", ["value", "ip", "integration_id"])

    op.drop_column("blocklisted_ips", "pool_id")
    # ### end Alembic commands ###
