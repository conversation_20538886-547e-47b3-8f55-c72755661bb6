import json
import logging
import os
from concurrent.futures import ThreadPoolExecutor

import botocore.session
import environ
import injections
import requests
from sqlalchemy import create_engine

from isolated_proxy import storages
from isolated_proxy.config import AppConfig

logger = logging.getLogger(__name__)


def make_cfg():
    return environ.to_config(AppConfig, environ=os.environ)


def make_inj(cfg):
    inj = injections.Container()

    inj["config"] = cfg

    # TODO: use custom utils.dumps and utils.loads
    inj["botocore_session"] = botocore.session.get_session()

    config = botocore.config.Config(
        read_timeout=600,
        connect_timeout=600,
    )

    for client_type in ["s3", "sqs", "secretsmanager", "lambda"]:
        inj[f"{client_type}_client"] = inj["botocore_session"].create_client(
            client_type,
            region_name=cfg.aws_region,
            config=config,
        )

    inj["requests_session"] = requests.Session()

    inj["brightdata_session"] = requests.Session()
    inj["brightdata_session"].headers.update({"Authorization": f"Bearer {cfg.brightdata_api_token}"})

    inj["executor"] = ThreadPoolExecutor(max_workers=cfg.executor_workers)

    import geoip2.database

    inj["geo_city"] = geoip2.database.Reader(cfg.geo_ip.city_path)
    inj["geo_country"] = geoip2.database.Reader(cfg.geo_ip.country_path)

    pg_kwargs = {
        "echo": cfg.debug and cfg.db.name != "test",
        "pool_pre_ping": True,
        "connect_args": {
            "keepalives": 1,
            "keepalives_idle": 30,
            "keepalives_interval": 10,
            "keepalives_count": 5,
        },
    }

    if cfg.debug:
        pg_url = f"postgresql+psycopg2://{cfg.db.username}:{cfg.db.password}@{cfg.db.proxy_endpoint}:{cfg.db.port}/{cfg.db.name}"
    else:
        secret_response = inj["secretsmanager_client"].get_secret_value(SecretId=cfg.db.secret_arn)
        secrets = json.loads(secret_response["SecretString"])

        pg_url = f"postgresql+psycopg2://{secrets['username']}:{secrets['password']}@{cfg.db.proxy_endpoint}:{cfg.db.port}/{cfg.db.name}"

    inj["pg"] = create_engine(pg_url, **pg_kwargs)

    inj["pg_storage"] = storages.PGStorage(pg=inj["pg"])

    return inj


def make_all():
    cfg = make_cfg()

    inj = make_inj(cfg)

    inj.interconnect_all()

    return cfg, inj
