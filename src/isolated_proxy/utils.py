import datetime
import json
import uuid
from decimal import Decimal
from functools import lru_cache
from pathlib import Path

import orjson
import xxhash
from yarl import URL


def build_proxy(ip, proxy_data):
    return f"https://{proxy_data['username']}-{ip}:{proxy_data['password']}@{proxy_data['host']}"


@lru_cache(maxsize=5)
def get_zone_creds(zone_name):
    from isolated_proxy import inj

    pool_data = inj["pg_storage"].proxy_pools.get(pool_id=zone_name).first()
    username = pool_data["username"]
    password = pool_data["password"]

    return username, password


def build_proxy_single(ip, zone_name):
    from isolated_proxy import cfg

    username, password = get_zone_creds(zone_name)

    return f"https://{username}-{ip}:{password}@{cfg.host}"


def get_proxy_for_playwright(proxy):
    proxy_ = URL(proxy)

    return {
        "server": f"{proxy_.host}:{proxy_.port}",
        "username": proxy_.user,
        "password": proxy_.password,
    }


def default(obj):
    if isinstance(obj, Path):
        return str(Path)

    if isinstance(obj, Decimal):
        return str(obj)

    if isinstance(obj, uuid.UUID):
        return obj.hex()

    if isinstance(obj, URL):
        return str(obj)

    if isinstance(obj, datetime.time):
        return str(obj)

    raise NotImplementedError(f"{repr(obj)} is not supported!")


def loads(*args, **kwargs):
    try:
        return orjson.loads(*args, **kwargs)
    except Exception:
        return json.loads(*args)


def dumps(*args, **kwargs):
    try:
        return orjson.dumps(
            *args,
            default=default,
            option=orjson.OPT_NON_STR_KEYS,
            **kwargs,
        ).decode("utf-8")
    except Exception:
        return json.dumps(*args)


def chunks(lst, n):
    for i in range(0, len(lst), n):
        yield lst[i : i + n]  # noqa


def make_xxhash(value):
    return xxhash.xxh64(value).intdigest()


def scan_table(table, columns=None):
    scan_kwargs = {}
    if columns:
        scan_kwargs["ProjectionExpression"] = ", ".join(columns)
    done = False
    start_key = None
    items = []

    while not done:
        if start_key:
            scan_kwargs["ExclusiveStartKey"] = start_key
        response = table.scan(**scan_kwargs)
        items.extend(response.get("Items", []))
        start_key = response.get("LastEvaluatedKey", None)
        done = start_key is None

    return items
