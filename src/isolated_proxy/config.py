import logging

import environ

from isolated_proxy.enums import ProxyStrategyEnum


@environ.config(prefix="isolated_proxy")
class AppConfig:
    @environ.config
    class Sentry:
        url = environ.var(None)

    @environ.config
    class DB:
        url = environ.var("127.0.0.1")
        proxy_endpoint: str = environ.var("127.0.0.1")
        port = environ.var(5432, converter=int)
        name = environ.var("isolated_proxy")
        username = environ.var("isolated_proxy")
        password = environ.var("isolated_proxy")
        secret_arn = environ.var("arn:aws:secretsmanager:us-east-1:749603930913:secret:isolated-proxy-develop-DBClusterSecretProvisioned-HPcqHK")

    @environ.config
    class Geoip:
        city_path = environ.var("GeoLite2-City.mmdb")
        country_path = environ.var("GeoLite2-Country.mmdb")

    @environ.config
    class Slack:
        token: str = environ.var("*********************************************************")

        channel_main_notifications: str = environ.var("C05TE0FCRU2")
        channel_dev_notifications: str = environ.var("C032A49NUSY")
        user_name: str = environ.var("Isolated Proxy Service")
        tag_user_id: str = environ.var("<@U01MVRB6A4U>")
        # seconds
        run_timeout_min: int = environ.var(3600, converter=int)
        run_timeout_max: int = environ.var(7200, converter=int)
        message_count_threshold: int = environ.var(10, converter=int)

    debug = environ.bool_var("true")
    browser_debug = environ.bool_var("true")
    is_lambda: bool = environ.var(False, name="AWS_LAMBDA_RUNTIME_API", converter=bool)
    aws_region = environ.var("us-east-1")
    logging_level = environ.var(logging.INFO, converter=int)
    stage = environ.var("develop")
    notify_slack_users: str = environ.var("false", converter=str)
    project_name = environ.var("isolated_proxy")
    executor_workers: int = environ.var(8, converter=int)
    min_balance_smart_proxy = 50.0  # 50$
    min_balance_brightdata = 100.0  # 100$

    sentry = environ.group(Sentry)
    geo_ip: Geoip = environ.group(Geoip)

    bucket_name: str = environ.var("isolated-proxy-develop-bucket")

    db: DB = environ.group(DB)
    slack: Slack = environ.group(Slack)

    brightdata_api_token: str = environ.var("649aa341-46eb-47e1-ab39-374134111665")
    proxy_host = environ.var("brd.superproxy.io:22225")
    locations_ip_counter = environ.var(50, converter=int)
    proxy_customer = environ.var("cookdash")
    dispute_pool_name: str = environ.var("plutus_dm_pool")
    plutus_pool_name: str = environ.var("plutus_om_pool")
    atlas_pool_name: str = environ.var("plutus_atlas_pool")
    host: str = environ.var("brd.superproxy.io:22225")
    expiring_days = environ.var(3, converter=int)

    logic = environ.var(ProxyStrategyEnum.single_pool.value)  # single_pool, multi_pool
