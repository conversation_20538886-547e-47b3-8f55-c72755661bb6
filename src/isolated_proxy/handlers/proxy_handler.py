import datetime
import logging
import math
import random
import re
import typing as t
from functools import lru_cache

import pytz
import requests
import sqlalchemy as sa
from sqlalchemy import and_, or_
from sqlalchemy.sql.expression import false
from yarl import URL

from isolated_proxy import cfg, inj
from isolated_proxy.brightdata import ips as bright_ips
from isolated_proxy.brightdata import zones
from isolated_proxy.enums import (
    BlockProxyRequest,
    GeoData,
    IntegrationType,
    IpParamsMulti,
    IpParamsSingle,
    PoolStatus,
    ProxyData,
    ProxyDBData,
    ProxyRequest,
)
from isolated_proxy.handlers.slack_handler import <PERSON>lackHandler
from isolated_proxy.utils import (
    build_proxy,
    build_proxy_single,
    chunks,
    get_proxy_for_playwright,
    make_xxhash,
)

logger = logging.getLogger(__name__)

executor = inj["executor"]
s3_client = inj["s3_client"]
geo_ip = inj["geo_country"]
geo_city = inj["geo_city"]
storage = inj["pg_storage"]


class BaseProxyRotator:
    country = re.compile('<div class="info_2">(.*)</div>')
    base_url = URL("https://brightdata.com/api/zone/route_ips")
    my_ip = URL("https://www.myip.com")
    luminaty_my_ip = URL("https://lumtest.com/myip.json")

    def _get_geo_data(self, ip: t.Optional[str]) -> GeoData:
        offset = random.uniform(-0.001, 0.001)

        if ip is None:
            timezone_id = "America/Chicago"
            longitude = 41.881832
            latitude = -87.623177
        else:
            response = geo_city.city(ip)

            timezone_id = response.location.time_zone
            longitude = response.location.longitude
            latitude = response.location.latitude

        timezone_id = timezone_id
        longitude = round(longitude + offset, 6)
        latitude = round(latitude + offset, 6)

        return {
            "timezone_id": timezone_id,
            "longitude": longitude,
            "latitude": latitude,
        }

    @lru_cache(maxsize=128)
    def get_smart_proxy(self, residential_type) -> ProxyData:

        if residential_type:
            db_proxy = storage.proxy_pools.get(project_name=residential_type, residential=True).first()
        else:
            db_proxy = storage.proxy_pools.get(default_proxy=True).first()

        if not db_proxy:
            logger.info(f"Unknown residential_type {residential_type}")  # noqa
            raise Exception(f"Can't find residential proxy: {residential_type}")

        proxy_data = {"server": "us.smartproxy.com", "port": "10000", "username": db_proxy["username"], "password": db_proxy["password"]}

        geo_data = self._get_geo_data(None)

        return t.cast(
            ProxyData,
            {
                "ip": None,
                "proxy": f"http://{proxy_data['username']}:{proxy_data['password']}@{proxy_data['server']}:{proxy_data['port']}",
                "pw_proxy": proxy_data,
                **geo_data,
            },
        )


class MultiProxyRotator(BaseProxyRotator):
    def __init__(self, chain_id, location_count):
        self.chain_id = chain_id
        self.location_count = location_count

    def _rotate_proxy(
        self,
        ip: str,
        proxy_data: ProxyDBData,
    ) -> ProxyData:

        geo_data = self._get_geo_data(ip)
        proxy = build_proxy(ip, proxy_data=proxy_data)

        return t.cast(
            ProxyData,
            {
                "ip": ip,
                "proxy": proxy,
                "pw_proxy": get_proxy_for_playwright(proxy),
                **geo_data,
            },
        )

    def _get_username(self):
        return f"brd-customer-hl_0f816828-zone-{self.proxy_zone_name}-ip"

    @property
    def proxy_zone_name(self):
        return "ip_" + str(make_xxhash(self.chain_id))

    def _refresh_ips(self, _ips):
        response = bright_ips.refresh(zone_name=self.proxy_zone_name, country="us", ips=list(_ips))
        if response.status_code != 200:
            msg = "Can't refresh ips %(chain_id)s, %(response)s"
            context = {"chain_id": self.chain_id, "response": response.content}
            logger.exception(msg, context)
            raise Exception("Can't refresh ips")

        return response.json()["ips"]

    def refresh_and_check(self, _ips, pool):
        proxy_data = {
            "api_token": cfg.brightdata_api_token,
            "pool_name": self.chain_id,
            "username": self._get_username(),
            "password": pool["password"],
            "host": cfg.proxy_host,
        }

        new_ips = self._refresh_ips(_ips)

        us_ips = self._check_proxies(
            [{"ip": ip, "proxy_data": proxy_data} for ip in new_ips],
            datetime.datetime.utcnow(),
        )

        difference = set(new_ips) - set(us_ips)
        if not difference:
            return us_ips

        new_ips = self._refresh_ips(difference)
        new_us_ips = self._check_proxies(
            [{"ip": ip, "proxy_data": proxy_data} for ip in new_ips],
            datetime.datetime.utcnow(),
        )

        difference = set(new_ips) - set(new_us_ips)
        if difference:
            logger.warning("Can't get us ips for %(chain_id)s while trying to refresh", {"chain_id": self.chain_id})
        return us_ips + new_us_ips

    def refresh_proxy_ip(self, _ips):
        pool = storage.proxy_pools.get(pool_id=self.chain_id)

        if not pool:
            msg = "Can't find pool %(chain_id)s"
            context = {"chain_id": self.chain_id}
            logger.warning(msg, context)
            raise Exception("Can't find pool")

        ip_exists = storage.ips.get(pool_id=self.chain_id, ip__in=_ips, disabled=False, _columns=["ip"])
        if not ip_exists:
            msg = "Can't find ip for pool %(chain_id)s"
            context = {"chain_id": self.chain_id}
            logger.warning(msg, context)
            raise Exception("Can't find ip for pool")

        new_ips = self.refresh_and_check(_ips, pool[0])

        storage.ips.bulk_upsert(
            [{"pool_id": self.chain_id, "ip": ip} for ip in new_ips],
        )
        storage.ips.delete(pool_id=self.chain_id, ip__in=_ips)

        return new_ips

    def get_proxy(self, event) -> ProxyData:
        if event.get("residential"):
            return self.get_smart_proxy(event.get("residential_type"))

        pool = storage.proxy_pools.get(pool_id=self.chain_id)

        if not pool:
            self.create_zone()
            pool = storage.proxy_pools.get(pool_id=self.chain_id)

        pool = pool[0]

        if pool["status"] == PoolStatus.disabled.value:
            msg = "Pool %(chain_id)s is disabled"
            context = {"chain_id": self.chain_id}
            logger.warning(msg, context)
            raise Exception("Pool is disabled")

        new_ips_count = self._resolve_ips_count()
        old_ips_count = len(storage.ips.get(pool_id=self.chain_id, disabled=False))

        if new_ips_count > old_ips_count:
            self.add_ips_to_pool(new_ips_count - old_ips_count, pool)

        chain_ips = storage.ips.get(pool_id=self.chain_id, disabled=False, _columns=["ip"])
        ip = random.choice(chain_ips)["ip"]

        return self._rotate_proxy(
            ip=ip,
            proxy_data={
                "api_token": cfg.brightdata_api_token,
                "pool_name": self.chain_id,
                "username": self._get_username(),
                "password": pool["password"],
                "host": cfg.proxy_host,
            },
        )

    def add_ips_to_pool(self, new_ips_count, pool):
        username = self._get_username()
        proxy_data = {
            "api_token": cfg.brightdata_api_token,
            "pool_name": self.chain_id,
            "username": username,
            "password": pool["password"],
            "host": cfg.proxy_host,
        }
        response = bright_ips.add_ip(zone_name=self.proxy_zone_name, count=new_ips_count, country="us")
        if response.status_code != 200:
            msg = "Can't add ips %(chain_id)s, %(response)s"
            context = {"chain_id": self.chain_id, "response": response.content}
            logger.exception(msg, context)
            raise Exception("Can't add ips")
        new_ips = response.json()["ips"]
        if not new_ips:
            msg = "Can't get ips for %(chain_id)s"
            context = {"chain_id": self.chain_id}
            logger.warning(msg, context)
            raise Exception("Can't get ips")
        us_ips = self._check_proxies([{"ip": ip, "proxy_data": proxy_data} for ip in new_ips], datetime.datetime.utcnow())
        non_us_ips = set(new_ips) - set(us_ips)
        if non_us_ips:
            new_ips = self._refresh_ips(list(non_us_ips))
            new_us_ips = self._check_proxies([{"ip": ip, "proxy_data": proxy_data} for ip in new_ips], datetime.datetime.utcnow())
            us_ips.extend(new_us_ips)
        storage.ips.bulk_upsert(
            [{"pool_id": self.chain_id, "ip": ip} for ip in us_ips],
        )

    def create_zone(self):
        response = zones.create(zone_name=self.proxy_zone_name)
        if response.status_code != 200 and response.content != b"Duplicate zone name":
            msg = "Can't create zone %(chain_id)s, %(response)s"
            context = {"chain_id": self.chain_id, "response": response.content}
            logger.exception(msg, context)
            raise Exception("Can't create zone")
        zone_data = zones.get(self.proxy_zone_name).json()
        password = zone_data["password"][0]
        response = zones.switch_100_uptime(zone_name=self.proxy_zone_name)
        if response.status_code != 200:
            msg = "Can't switch 100 uptime %(chain_id)s, %(response)s"
            context = {"chain_id": self.chain_id, "response": response.content}
            logger.exception(msg, context)
            raise Exception("Can't switch 100 uptime")

        storage.proxy_pools.upsert(
            pool_id=self.chain_id,
            password=password,
            username=self._get_username(),
            status=PoolStatus.active.value,
        )

        return password

    def _check_proxies(
        self,
        ips: t.List[IpParamsMulti],
        start: datetime.datetime,
    ) -> t.List[str]:
        us_ips = []

        chunk_size = 50

        for count, chunk in enumerate(chunks(ips, chunk_size)):
            if (datetime.datetime.utcnow() - start).seconds >= 90:
                break

            msg = "Processing %(from)i - %(to)i, %(all)i"
            context = {
                "from": count * chunk_size,
                "to": (count + 1) * chunk_size if (count + 1) * chunk_size < len(ips) else len(ips),
                "all": len(ips),
            }
            logger.info(msg, context)

            us_ips.extend(list(executor.map(self._check_proxy, chunk)))

        return list(filter(None, us_ips))

    def _check_proxy(self, params: IpParamsMulti):
        ip = params["ip"]

        geo_country = geo_ip.country(ip)

        if geo_country.country.iso_code != "US":
            return None

        proxy = build_proxy(ip, proxy_data=params["proxy_data"])

        def __luminaty_request():
            response = requests.get(
                str(self.luminaty_my_ip),
                proxies={
                    "http": proxy,
                    "https": proxy,
                },
            )
            return response.json().get("country")

        try:
            country_luminaty = __luminaty_request()
        except Exception as exc:
            logger.info(exc, exc_info=exc)
            try:
                country_luminaty = __luminaty_request()
            except Exception as exc:
                logger.info(exc, exc_info=exc)
                return None

        if not country_luminaty:
            return None

        if country_luminaty != "US":
            return None

        return ip

    def _resolve_ips_count(self):
        val = math.ceil(self.location_count / cfg.locations_ip_counter)
        if val < 2:  # because min ips count in proxy pool is 2
            return 2
        return val

    def update_pool_status(self, new_status):
        pool = storage.proxy_pools.get(pool_id=self.chain_id)

        if not pool:
            msg = "Can't find pool %(chain_id)s"
            context = {"chain_id": self.chain_id}
            logger.warning(msg, context)
            raise Exception("Can't find pool")

        pool = pool[0]
        num_status = "1" if new_status == PoolStatus.disabled.value else "0"
        if pool["status"] == new_status:
            msg = "Pool %(chain_id)s is already %(status)s"
            context = {"chain_id": self.chain_id, "status": new_status}
            logger.warning(msg, context)
            raise Exception("Pool is already in this status")

        response = zones.update_zone(zone_name=self.proxy_zone_name, disable=num_status)
        if response.status_code != 200:
            msg = "Can't inactivate zone %(chain_id)s, %(response)s"
            context = {"chain_id": self.chain_id, "response": response.content}
            logger.exception(msg, context)
            raise Exception("Can't update zone status")

        storage.ips.delete(pool_id=self.chain_id)
        proxy_data = {
            "api_token": cfg.brightdata_api_token,
            "pool_name": self.proxy_zone_name,
            "username": pool["username"],
            "password": pool["password"],
            "host": cfg.proxy_host,
        }

        zone_stats = zones.zone_stats(self.proxy_zone_name).json()
        zone_ips = [item["ip"] for item in zone_stats["ips"]]

        us_ips = self._check_proxies(
            [{"ip": ip, "proxy_data": proxy_data} for ip in zone_ips],
            datetime.datetime.utcnow(),
        )
        if len(us_ips) != len(zone_ips):
            msg = "Refreshing %(ips)s for %(chain_id)s"
            context = {"ips": list(set(zone_ips) - set(us_ips)), "chain_id": self.chain_id}
            logger.info(msg, context)
            us_ips = us_ips + self.refresh_and_check(list(set(zone_ips) - set(us_ips)), pool)

        storage.proxy_pools.upsert(
            pool_id=self.chain_id,
            password=pool["password"],
            username=pool["username"],
            status=new_status,
        )
        storage.ips.bulk_upsert(
            [{"pool_id": self.chain_id, "ip": ip} for ip in us_ips],
        )

        return True


class SingleProxyRotator(BaseProxyRotator):
    country = re.compile(r'<div class="info_2">(.*)</div>')
    base_url: URL = URL("https://brightdata.com/api/zone/route_ips")
    my_ip: URL = URL("https://www.myip.com")
    luminaty_my_ip: URL = URL("https://lumtest.com/myip.json")
    session: requests.Session
    pool_ips: t.Dict[str, t.List[str]]

    def __init__(self) -> None:
        self.session = requests.Session()
        self.session.headers.update({"Authorization": f"Bearer {cfg.brightdata_api_token}"})
        self.pool_ips = {}
        expiry_date = pytz.utc.localize(datetime.datetime.utcnow()) - datetime.timedelta(hours=6)

        self.pools = {item["project_name"]: item["pool_id"] for item in storage.proxy_pools.get(residential=False)}

        for pool_id in self.pools.values():
            model = storage.ips.model

            conditions = [
                and_(model.c.updated_at > expiry_date),
                and_(model.c.updated_at == None, model.c.created_at > expiry_date),  # noqa
            ]

            query = sa.select(model.c.ip).where(or_(*conditions)).where(model.c.pool_id == pool_id).where(model.c.disabled == false())

            with storage.pg.connect() as conn:
                result = conn.execute(query).fetchall()
                mapping = [dict(row._mapping) for row in result]

            self.pool_ips[pool_id] = [row["ip"] for row in mapping]

    def _check_proxy(self, params: IpParamsSingle) -> t.Optional[str]:
        ip = params["ip"]
        proxy = params["proxy"]

        try:
            geo_country = geo_ip.country(ip)

            if geo_country.country.iso_code != "US":
                return None

            response = requests.get(
                str(self.luminaty_my_ip),
                proxies={
                    "http": proxy,
                    "https": proxy,
                },
            )
            country_luminaty = response.json().get("country")
        except Exception as exc:
            logger.info(exc, exc_info=exc)
            return None

        if not country_luminaty:
            return None

        if country_luminaty != "US":
            return None

        return ip

    def _check_proxies(self, ips: t.List[IpParamsSingle]) -> t.List[str]:
        us_ips: t.List[str] = []

        chunk_size = 50

        for count, chunk in enumerate(chunks(ips, chunk_size)):
            msg = "Processing %(from)i - %(to)i, %(all)i"
            context = {
                "from": count * chunk_size,
                "to": (count + 1) * chunk_size if (count + 1) * chunk_size < len(ips) else len(ips),
                "all": len(ips),
            }
            logger.info(msg, context)

            us_ips.extend(list(inj["executor"].map(self._check_proxy, chunk)))

        return list(filter(None, us_ips))

    def set_proxies(self) -> None:
        self._set_integration_proxies()

    def _set_integration_proxies(self) -> None:
        for project_type, zone_name in self.pools.items():
            url = self.base_url.with_query(
                {
                    "zone": zone_name,
                    "country": "us",
                },
            )

            response = self.session.get(str(url))

            ips = sorted(response.text.split("\n"))

            us_ips = self._check_proxies(
                [{"ip": ip, "proxy": build_proxy_single(ip, zone_name=zone_name)} for ip in ips],
            )

            total_ips = len(ips)
            us_ips_count = len(us_ips)
            us_ips_percentage = (us_ips_count / total_ips * 100) if total_ips > 0 else 0

            msg = (
                "%(project_type)s, %(us_ips_count)s IPs set, total %(total_ips)s IPs, %(us_ips_percentage)s%% US IPs (%(us_ips_count)s/%(total_ips)s)"
            )
            context = {
                "project_type": project_type,
                "us_ips_percentage": us_ips_percentage,
                "us_ips_count": us_ips_count,
                "total_ips": total_ips,
            }
            logger.info(msg, context)

            db_ips_count = len(storage.ips.get(pool_id=zone_name, disabled=False))

            if project_type != "atlas" and (us_ips_percentage < 50 or us_ips_count != db_ips_count):
                message = f"{db_ips_count} -> {us_ips_count}/{total_ips} IPs set for {project_type}"
                if us_ips_percentage < 50:
                    message = f"{cfg.slack.tag_user_id}, {message}"

                SlackHandler().send_message(
                    message=message,
                    channel=cfg.slack.channel_dev_notifications,
                )

            with storage.pg.begin() as tr:
                storage.ips.bulk_upsert(
                    [{"pool_id": zone_name, "ip": ip, "disabled": False} for ip in us_ips],
                    tr=tr,
                )

                stmt = (
                    sa.update(storage.ips.model)
                    .where(
                        sa.and_(
                            storage.ips.model.c.pool_id == zone_name,
                            storage.ips.model.c.ip.notin_(us_ips),
                            storage.ips.model.c.disabled == false(),
                        ),
                    )
                    .values(disabled=True)
                )
                tr.execute(stmt)

    def _get_ip(
        self,
        value: str,
        integration_type: str,
        retry_counter: int,
        ips: t.List[str],
        project_type: str,
        exclude: t.List[str] = None,
    ) -> str:
        hash_value = make_xxhash(value)
        index = (hash_value % len(ips)) - retry_counter

        msg = "value %(value)s, integration_type %(integration_type)s, retry_counter %(retry_counter)s, ips %(ips)s, index %(index)s, hash_value %(hash_value)s"  # noqa
        context = {
            "value": value,
            "integration_type": integration_type,
            "retry_counter": retry_counter,
            "ips": len(ips),
            "index": index,
            "hash_value": hash_value,
        }
        logger.info(msg, context)

        integration_id = IntegrationType(integration_type).name

        if exclude:
            blocklisted_ips = set(exclude)
        else:
            result = storage.blocklisted_ips.get(
                integration_id=integration_id,
                value=value,
                _columns=["ip"],
                expiring_at__gt=datetime.datetime.utcnow(),
                pool_id=self.pools[project_type],
            )

            blocklisted_ips = {row["ip"] for row in result}

        if ips[index] not in blocklisted_ips:
            return t.cast(str, ips[index])

        index = sum([int(item) for item in list(str(hash_value))]) % len(ips)

        for ip in ips[index:] + ips[:index]:
            if ip not in blocklisted_ips:
                return ip

        msg = "No ip found after blocklist validation, pool_id: %(pool_id)s, %(integration_id)s"
        context = {"pool_id": self.pools[project_type], "integration_id": integration_id}
        logger.warning(msg, context)

        message = f"{cfg.slack.tag_user_id} <@U052X18P59N> No ip found after blocklist validation, pool_id: {self.pools[project_type]}, integration_id: {integration_id}"  # noqa
        SlackHandler().send_message(
            message=message,
            channel=cfg.slack.channel_dev_notifications,
        )

        result = storage.blocklisted_ips.delete(
            integration_id=integration_id,
            pool_id=self.pools[project_type],
        )

        return self._get_ip(value, integration_type, retry_counter, ips, project_type)

    def _rotate_proxy(
        self,
        value: str,
        integration_type: str,
        project_type: str,
        retry_counter: int = 0,
        exclude: t.List[str] = None,
        **kwargs,
    ) -> ProxyData:

        zone_name = self.pools[project_type]

        ips = self.pool_ips[zone_name]

        if not ips:
            raise Exception("No ips found")

        ip = self._get_ip(
            value,
            integration_type,
            retry_counter,
            ips,
            project_type,
            exclude,
        )

        geo_data = self._get_geo_data(ip)
        proxy = build_proxy_single(ip, zone_name=self.pools[project_type])

        return t.cast(
            ProxyData,
            {
                "ip": ip,
                "proxy": proxy,
                "pw_proxy": get_proxy_for_playwright(proxy),
                **geo_data,
            },
        )

    def get_proxy(self, payload: ProxyRequest) -> ProxyData:
        if payload["residential"]:
            return self.get_smart_proxy(payload.get("residential_type"))

        return self._rotate_proxy(**payload)

    def block_proxy(self, payload: BlockProxyRequest) -> None:
        ips_list = [ip for ip in payload["ips"] if ip]
        if not ips_list:
            logger.info("No ips to block")
            return

        data = [
            {
                "integration_id": IntegrationType[payload["integration_type"]].value,
                "value": payload["value"],
                "ip": item,
                "expiring_at": datetime.datetime.utcnow() + datetime.timedelta(days=cfg.expiring_days),
                "pool_id": self.pools[payload["project_type"]],
            }
            for item in ips_list
        ]

        storage.blocklisted_ips.bulk_upsert(data)
