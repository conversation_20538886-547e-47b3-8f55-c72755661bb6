import logging

import injections
import requests
from yarl import URL

from isolated_proxy import cfg
from isolated_proxy.errors import MoneyAmountException

logger = logging.getLogger(__name__)


class SlackHandler:
    slack_url = URL("https://slack.com/api")
    slack_post_url = slack_url / "chat.postMessage"

    def __init__(self):
        self.session = requests.Session()

        headers = {"Authorization": f"Bearer {cfg.slack.token}"}
        self.session.headers.update(headers)

    def send_message(self, message, channel=cfg.slack.channel_dev_notifications, username=cfg.slack.user_name):
        payload = {
            "channel": channel,
            "text": message,
            "username": username,
        }

        if cfg.debug or cfg.notify_slack_users == "false":
            msg = "Slack message in debug mode. Message: %(message)s"
            context = {"message": message}
            logger.info(msg, context)
            return None  # noqa

        ret = self.session.post(
            str(self.slack_post_url),
            json=payload,
        ).json()

        if not ret["ok"] is True:
            msg = "Send to slack went wrong %(desc)s"
            context = {"desc": ret}
            logger.warning(msg, context)
            raise Exception


@injections.has
class SlackIsolatedLoginErrors(SlackHandler):
    def create_message(self, *, data):
        if not data:
            return

        body_text = f"Error: {data['error']}\n"
        body_text += f"Name: {data.get('name')}\n"
        if data["error"] == MoneyAmountException.__name__:
            body_text += f"{cfg.slack.tag_user_id} Amount: {data['amount']}\n"
        text = f"Smart proxy error:\n\n{body_text}"

        self.send_message(
            message=text,
            channel=data.get("channel", cfg.slack.channel_dev_notifications),
        )
