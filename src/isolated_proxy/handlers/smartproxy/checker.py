import logging
import time
from datetime import datetime, timedelta, timezone

from plutus_core.decorators import auto_retry, extensive_logging
from plutus_core.handlers.browser import Browser
from selenium.common import TimeoutException, WebDriverException
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from isolated_proxy import cfg, errors
from isolated_proxy.functions.proxy import get_proxy
from isolated_proxy.handlers.smartproxy.constants import Constants

logger = logging.getLogger(__name__)

MAX_RETRIES = 3
RETRY_DELAY = 5
BALANCE_CHECK_TIMEOUT = 30
LOGIN_TIMEOUT = 45
CHECK_LOGIN_TIMEOUT = 10


class SmartProxyChecker:
    def __init__(self, webdriver, session_details):
        self.webdriver = webdriver
        self.stack = []
        self.is_finished = False
        self.session_details = session_details
        self.balance = None
        self.last_check_time = None
        self.failures = 0

    @extensive_logging
    @auto_retry(excludes=(errors.MoneyAmountException,), retries=MAX_RETRIES, delay=RETRY_DELAY)
    def check_balance(self):
        try:
            time.sleep(5)

            balance_element = WebDriverWait(self.webdriver, BALANCE_CHECK_TIMEOUT).until(
                EC.presence_of_element_located((By.XPATH, '//*[@id="regular-container"]/div[1]/div[1]/nav/div[2]/div[1]/div/div/div/div/div/p')),
            )
            str_balance = balance_element.get_attribute("innerHTML")
            self.balance = float(str_balance.replace("$", "").strip())
            self.last_check_time = datetime.now(timezone.utc)
            self.failures = 0

            msg = "Current balance: $%(balance)s"
            context = {"balance": self.balance}
            logger.info(msg, context)

            if self.balance < cfg.min_balance_smart_proxy:
                msg = "Balance $%(balance)s is below minimum threshold $%(threshold)s"
                context = {"balance": self.balance, "threshold": cfg.min_balance_smart_proxy}
                logger.warning(msg, context)
                raise errors.MoneyAmountException(f"SmartProxy balance (${self.balance}) is below minimum threshold (${cfg.min_balance_smart_proxy})")
        except TimeoutException as e:
            self.failures += 1
            msg = "Failed to find balance element (attempt %(attempt)s/%(max_retries)s)"
            context = {"attempt": self.failures, "max_retries": MAX_RETRIES}
            logger.error(msg, context)
            raise errors.SmartProxyException("Balance element not found") from e
        except ValueError as e:
            msg = "Failed to parse balance value: %(value)s"
            context = {"value": str_balance}
            logger.error(msg, context)
            raise errors.SmartProxyException("Invalid balance format") from e

    @extensive_logging
    def check_logged_in(self):
        try:
            WebDriverWait(self.webdriver, CHECK_LOGIN_TIMEOUT).until(
                EC.presence_of_element_located((By.XPATH, "//*[text()='Welcome to Smart Dashboard!']")),
            )
            logger.info("Successfully logged into SmartProxy dashboard")
        except TimeoutException as e:
            logger.error("Login verification failed - Welcome message not found")
            raise errors.LoginRetryException("Login verification failed - dashboard not loaded") from e
        except WebDriverException as e:
            msg = "Browser error during login verification: %(error)s"
            context = {"error": str(e)}
            logger.error(msg, context)
            raise errors.LoginException("Browser error during login verification") from e

    @extensive_logging
    def set_session_details(self):
        try:
            local_storage = self.session_details["local_storage"]
            self.webdriver.execute_script(
                f"Object.keys({local_storage}).forEach(key => {{window.localStorage.setItem(key, {local_storage}[key]);}});",
            )
            session_storage = self.session_details["session_storage"]
            self.webdriver.execute_script(
                f"Object.keys({session_storage}).forEach(key => {{window.sessionStorage.setItem(key, {session_storage}[key]);}});",
            )

            for cookie in self.session_details["cookies"]:
                self.webdriver.add_cookie(cookie)

            self.webdriver.get(str(Constants.BASE_URL))
            logger.info("Successfully set session details and cookies")
        except Exception as e:
            msg = "Failed to set session details: %(error)s"
            context = {"error": str(e)}
            logger.error(msg, context)
            raise errors.LoginRetryException("Failed to set session details") from e

    @extensive_logging
    def check_credentials_expired(self):
        try:
            expired_element = WebDriverWait(self.webdriver, CHECK_LOGIN_TIMEOUT).until(
                EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Session expired. Please login again.')]")),
            )
            if expired_element:
                msg = "Session expired, need to relogin with credentials"
                context = {"username": self.session_details["username"]}
                logger.warning(msg, context)
                return True
        except TimeoutException:
            return False
        return False

    def check_captcha(self):
        text = "Verifying you are human"
        try:
            WebDriverWait(self.webdriver, 15).until(
                EC.presence_of_element_located((By.XPATH, f"//*[contains(., '{text}')]")),
            )
            logger.info("Captcha detected")
            raise errors.CaptchaException("Captcha detected")
        except TimeoutException:
            logger.info("No captcha detected")

    @extensive_logging
    def go_to_login_page(self):
        try:
            self.webdriver.get(str(Constants.BASE_URL))
            logger.info("Navigated to login page")
        except WebDriverException as e:
            msg = "Failed to navigate to login page: %(error)s"
            context = {"error": str(e)}
            logger.error(msg, context)
            raise errors.LoginException("Failed to navigate to login page") from e

    @extensive_logging
    def credentials_login(self):
        try:
            username_field = WebDriverWait(self.webdriver, LOGIN_TIMEOUT).until(EC.presence_of_element_located((By.XPATH, '//*[@id="email"]')))
            username_field.send_keys(self.session_details["username"])

            password_field = self.webdriver.find_element(By.XPATH, '//*[@id="password"]')
            password_field.send_keys(self.session_details["password"])

            logger.info("Attempting login with credentials")
            login_button = self.webdriver.find_element(By.XPATH, "//*[text()='Log In']")
            login_button.click()

            self.check_logged_in()

            cookies = self.webdriver.get_cookies()
            self.session_details["cookies"] = cookies
            self.session_details["local_storage"] = self.webdriver.execute_script("return window.localStorage;")
            self.session_details["session_storage"] = self.webdriver.execute_script("return window.sessionStorage")
            self.session_details["expiring_at"] = datetime.utcnow() + timedelta(minutes=5000)

            logger.info("Login successful - session details updated")
        except TimeoutException as e:
            logger.error("Login failed - elements not found")
            raise errors.LoginException("Login failed - form elements not found") from e
        except WebDriverException as e:
            msg = "Browser error during login: %(error)s"
            context = {"error": str(e)}
            logger.error(msg, context)
            raise errors.LoginException("Browser error during login") from e

    def set_stack(self):
        if self.session_details.get("expiring_at") and self.session_details["expiring_at"] > datetime.now(tz=timezone.utc):
            logger.info("Using existing session")
            self.stack.append(self.check_balance)
            self.stack.append(self.check_logged_in)
            self.stack.append(self.set_session_details)
            self.stack.append(self.check_captcha)
            self.stack.append(self.go_to_login_page)
        else:
            logger.info("Starting new session")
            self.stack.append(self.check_balance)
            self.stack.append(self.credentials_login)
            self.stack.append(self.check_captcha)
            self.stack.append(self.go_to_login_page)

    def resume(self):
        while self.stack:
            func = self.stack.pop()
            try:
                func()
                if func == self.set_session_details and self.check_credentials_expired():
                    logger.info("Detected expired session, switching to credentials login")
                    self.stack = [
                        self.check_balance,
                        self.credentials_login,
                        self.check_captcha,
                        self.go_to_login_page,
                    ]
            except errors.MoneyAmountException as e:
                self.stack = []
                raise e
            except errors.LoginRetryException:
                self.stack = [
                    self.check_balance,
                    self.credentials_login,
                    self.check_captcha,
                    self.go_to_login_page,
                ]
            except (errors.LoginException, errors.SmartProxyException) as e:
                msg = "Operation failed: %(error)s"
                context = {"error": str(e)}
                logger.error(msg, context)
                self.stack = []
                raise
            except Exception as e:
                msg = "Unexpected error during %(function)s: %(error)s"
                context = {"function": func.__name__, "error": str(e)}
                logger.error(msg, context)
                self.stack = []
                raise e

        self.is_finished = True
        logger.info("SmartProxy check completed successfully")

    def start(self):
        self.set_stack()
        self.resume()


class BalanceCheckerManager:
    def __init__(self, session_details, slack_handler, config, s3_client):
        self.config = config
        self.s3_client = s3_client
        self.proxy_data = None
        self.checker_cls = SmartProxyChecker
        self.session_details = session_details
        self.slack_handler = slack_handler
        self.error = None
        self.browser = None

    def _get_proxy(self):
        msg = "Getting proxy configuration"
        context = {"project_type": "dispute"}
        logger.info(msg, context)

        proxy_response = get_proxy(
            {
                "integration_type": "doordash",
                "value": "default",
                "retry_counter": 0,
                "location_count": 1,
                "project_type": "dispute",
                "residential": True,
                "exclude": [],
            },
            None,
        )
        return proxy_response["body"]

    def _initialize_browser(self):
        try:
            self.browser = Browser(
                self.proxy_data,
                cfg=self.config,
                s3_client=self.s3_client,
                single_process=True,
            )
            self.browser.start()
            logger.info("Browser initialized successfully")
        except Exception as e:
            msg = "Failed to initialize browser: %(error)s"
            context = {"error": str(e)}
            logger.exception(msg, context, exc_info=e)
            raise errors.SmartProxyException("Browser initialization failed") from e

    def _notify_slack(self, data):
        try:
            self.slack_handler.create_message(data=data)
            logger.info("Slack notification sent successfully")
        except Exception as e:
            msg = "Failed to send Slack notification: %(error)s"
            context = {"error": str(e)}
            logger.error(msg, context)

    def start(self):
        try:
            self.proxy_data = self._get_proxy()

            self._initialize_browser()

            checker = self.checker_cls(
                webdriver=self.browser.webdriver,
                session_details=self.session_details,
            )

            try:
                checker.start()
            except errors.MoneyAmountException as e:
                msg = "Low balance detected: %(error)s"
                context = {"error": str(e)}
                logger.warning(msg, context)
                self._notify_slack(
                    {
                        "error": e.__class__.__name__,
                        "amount": checker.balance,
                        "channel": cfg.slack.channel_main_notifications,
                        "name": self.session_details.get("name"),
                    },
                )
                self.error = e
                self.browser.screenshot()

            except Exception as e:
                msg = "Checker error: %(error)s"
                context = {"error": str(e)}
                logger.error(msg, context)
                error_data = {
                    "status": "error",
                    "error": e.__class__.__name__,
                    "name": self.session_details.get("name"),
                }
                if checker.balance is not None:
                    error_data["amount"] = checker.balance
                self._notify_slack(error_data)
                self.error = e

                self.browser.screenshot()

            finally:
                if self.browser:
                    self.browser.close()

        except Exception as e:
            msg = "Setup error: %(error)s"
            context = {"error": str(e)}
            logger.error(msg, context)
            self._notify_slack(
                {
                    "status": "error",
                    "error": f"Setup failed: {e.__class__.__name__}",
                },
            )
            self.error = e

        return self.session_details
