import logging

import requests
from plutus_core.decorators import auto_retry
from requests.exceptions import ProxyError
from yarl import URL

logger = logging.getLogger(__name__)


class RequestCheckerManager:
    ip_check_url = URL("https://ip.smartproxy.com/json")

    def __init__(self, storage, slack_handler, cfg):
        self.storage = storage
        self.slack_handler = slack_handler
        self.cfg = cfg

    def notify_slack(self, error, proxy_name):
        try:
            self.slack_handler.create_message(
                data={
                    "error": error,
                    "channel": self.cfg.slack.channel_main_notifications,
                    "name": proxy_name,
                },
            )
            logger.info("Slack notification sent successfully")
        except Exception as e:
            logger.error(f"Failed to send Slack notification: {str(e)}")  # noqa

    def get_proxy(self, proxy_type):
        db_proxy = self.storage.proxy_pools.get(pool_id=proxy_type).first()

        if not db_proxy:
            logger.warning(f"No proxy found for {proxy_type}")  # noqa
            return None

        proxy_data = {
            "server": "us.smartproxy.com",
            "port": "10000",
            "username": db_proxy["username"],
            "password": db_proxy["password"],
        }

        return {
            "proxy": f"http://{proxy_data['username']}:{proxy_data['password']}@{proxy_data['server']}:{proxy_data['port']}",
            "name": proxy_type,
        }

    @auto_retry
    def ip_request(self, proxy):
        return requests.get(
            str(self.ip_check_url),
            proxies={"http": proxy["proxy"], "https": proxy["proxy"]},
            timeout=10,
        )

    def start(self):
        for proxy_type in ["smartproxy", "atlas"]:
            proxy = self.get_proxy(proxy_type)

            if not proxy:
                continue

            try:
                self.ip_request(proxy)
            except ProxyError as exc:
                if exc.args[0].reason.original_error.args[0] == "Tunnel connection failed: 407 Proxy Authentication Required":
                    self.notify_slack(error="Proxy Error", proxy_name=proxy["name"])
            except Exception as exc:
                logger.info(f"Request failed: {exc}", exc_info=exc)  # noqa
