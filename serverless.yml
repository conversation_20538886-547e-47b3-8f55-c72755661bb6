service: isolated-proxy

configValidationMode: error

provider:
  name: aws
  region: ${self:custom.region}
  stage: ${self:custom.stage}
  stackName: ${self:custom.name}-${self:custom.stage}
  lambdaHashingVersion: 20201221
  runtime: python3.11
  vpc:
    securityGroupIds:
      - ${file(resources/${self:custom.stage}_env.yml):BASTION_SECURITY_GROUP}
    subnetIds:
      - ${file(resources/${self:custom.stage}_env.yml):SUBNET_ID1}
      - ${file(resources/${self:custom.stage}_env.yml):SUBNET_ID2}
  environment:
    ISOLATED_PROXY_AWS_REGION: ${self:custom.region}
    ISOLATED_PROXY_DEBUG: 'false'
    ISOLATED_PROXY_BROWSER_DEBUG: 'false'
    ISOLATED_PROXY_BUCKET_NAME: ${self:custom.s3.bucket}
    ISOLATED_PROXY_SENTRY_URL: ${file(resources/${self:custom.stage}_env.yml):SENTRY_DSN}
    SENTRY_DSN: ${file(resources/${self:custom.stage}_env.yml):SENTRY_DSN}
    SENTRY_TRACES_SAMPLE_RATE: "0.0"
    ISOLATED_PROXY_NOTIFY_SLACK_USERS: ${file(resources/${self:custom.stage}_env.yml):NOTIFY_SLACK_USERS}
    ISOLATED_PROXY_GEO_IP_CITY_PATH: ${file(resources/${self:custom.stage}_env.yml):GEO_IP_CITY_PATH}
    ISOLATED_PROXY_GEO_IP_COUNTRY_PATH: ${file(resources/${self:custom.stage}_env.yml):GEO_IP_COUNTRY_PATH}
    ISOLATED_PROXY_SQS_NEW_POOL_CREATION: ${self:custom.sqs.new_pool_creation}
    ISOLATED_PROXY_SQS_NEW_POOL_CREATION_DLQ: ${self:custom.sqs.new_pool_creation_dlq}
    ISOLATED_PROXY_DB_URL: !GetAtt IsolatedProxyDBInstance.Endpoint.Address
    ISOLATED_PROXY_DB_PORT: !GetAtt IsolatedProxyDBInstance.Endpoint.Port
    ISOLATED_PROXY_DB_NAME: isolated_proxy
    ISOLATED_PROXY_DB_SECRET_ARN: !Ref ClusterSecretProvisioned
    ISOLATED_PROXY_DB_PROXY_ENDPOINT: !GetAtt DBProxyProvisioned.Endpoint
    ISOLATED_PROXY_SLACK_CHANNEL_MAIN_NOTIFICATIONS: ${file(resources/${self:custom.stage}_env.yml):CHANNEL_MAIN_NOTIFICATIONS}
    ISOLATED_PROXY_SLACK_CHANNEL_DEV_NOTIFICATIONS: ${file(resources/${self:custom.stage}_env.yml):CHANNEL_DEV_NOTIFICATIONS}
  ecr:
    images:
      isolated-proxy:
        uri: ${env:PROXY_ECR_URL}
      isolated-proxy-smartproxy:
        uri: ${env:SMARTPROXY_ECR_URL}

functions:
  - ${file(resources/Functions.yml)}

resources:
  - ${file(resources/IAMRole.yml)}
  - ${file(resources/RDS.yml)}
  - ${file(resources/S3.yml)}

package:
  individually: true
  patterns:
    - '!env/**'
    - '!node_modules/**'
    - '!layers/**'
    - '!*.sublime*'
    - '!.git/**'
    - '!resources/**'
    - '!requirements/**'
    - '!.github/**'
    - '!build/**'
    - '!dist/**'

custom:
  name: isolated-proxy
  stage: ${env:BRANCH}
  region: ${env:AWS_REGION}
  s3:
    bucket: ${self:provider.stackName}-bucket
  sqs:
    new_pool_creation: ${self:custom.stage}-new-pool-creation
    new_pool_creation_dlq: ${self:custom.stage}-new-pool-creation-dlq
  prune:
    automatic: true
    includeLayers: true
    number: 3

plugins:
  - serverless-prune-versions
