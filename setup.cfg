[tool:brunette]
line-length=150
single-quotes=false

[tool:isort]
known_first_party=isolated_proxy
profile=black
multi_line_output=3

[flake8]
# format=${cyan}%(path)s${reset}:${yellow_bold}%(row)d${reset}:${green_bold}%(col)d${reset}: ${red_bold}%(code)s${reset} %(text)s
enable-extensions=G
inline-quotes="
multiline-quotes="
max-line-length=150
docstring-convention=google
extend-ignore=SIM119,D100,D103,D104,D101,D102,D105,D107,G200,G202,D415,D205,R503
per-file-ignores =
    tests/*:ASS001
[coverage:run]
branch=True
omit=site-packages

[easy_install]
zip_ok=False

[bdist_wheel]
universal=1
