version: '3.8'

services:
  isolated_proxy:
    build:
      context: compose/isolated_proxy
      dockerfile: ./Dockerfile
    image: isolated_proxy
    volumes:
      - ./:/opt/code
      - ./compose/isolated_proxy/entrypoint.sh:/opt/compose/entrypoint.sh
    environment:
      AWS_DEFAULT_REGION: ${AWS_REGION}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}

  postgres:
    image: postgres:13.9
    restart: always
    environment:
      POSTGRES_USER: isolated_proxy
      POSTGRES_PASSWORD: isolated_proxy
      POSTGRES_DB: isolated_proxy
    ports:
        - "5432:5432"
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
