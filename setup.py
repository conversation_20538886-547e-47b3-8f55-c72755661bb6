import re
from pathlib import Path

from setuptools import find_packages, setup

here = Path(__file__).parent

init = here / "src" / "isolated_proxy" / "__init__.py"

with init.open(mode="rt", encoding="utf-8") as fp:
    txt = fp.read()

pattern = r"^__version__ = \"([^\"]+)\"\r?$"
version = re.findall(pattern, txt, re.M)[0]

install_requires = []

requirements = here / "requirements" / "prod.txt"

with requirements.open(mode="rt", encoding="utf-8") as fp:
    _install_requires = (line.split("#")[0].strip() for line in fp)
    install_requires.extend(list(filter(None, _install_requires)))

setup(
    name="isolated_proxy",
    python_requires=">=3.10.0",
    version=version,
    install_requires=install_requires,
    include_package_data=True,
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    package_data={
        "": ["*.mmdb", "*.json", "*.ini"],
    },
    entry_points={
        "console_scripts": ["isolated_proxy = isolated_proxy.entrypoints.main:cli"],
    },
)
